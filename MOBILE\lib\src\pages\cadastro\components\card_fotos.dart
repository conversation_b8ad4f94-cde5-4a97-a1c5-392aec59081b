import 'dart:developer';
import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:dio/dio.dart';
//import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:path_provider/path_provider.dart';

// Utility function for downloading PDF
Future<File> downloadPDF(String url, String fileName) async {
  final Dio dio = Dio();
  final dir = await getApplicationDocumentsDirectory();
  final file = File('${dir.path}/$fileName');

  try {
    await dio.download(url, file.path);
    return file;
  } catch (e) {
    throw Exception('Error downloading PDF: $e');
  }
}

class CardFotos extends StatefulWidget {
  final String textFoto;
  final Function()? onTap;
  final String? imageUrl;

  const CardFotos({super.key, required this.textFoto, this.onTap, this.imageUrl});

  @override
  State<CardFotos> createState() => _CardFotosState();
}

class _CardFotosState extends State<CardFotos> {
  final ValueNotifier<bool> _loadingPdf = ValueNotifier(false);
  final ValueNotifier<File?> _pdfFile = ValueNotifier<File?>(null);

  Future<void> _loadPdf() async {
    try {
      _loadingPdf.value = true;
      _pdfFile.value = await downloadPDF(widget.imageUrl!, widget.imageUrl.hashCode.toString());
    } catch (e) {
      log('Erro ao carregar pdf: $e');
    } finally {
      _loadingPdf.value = false;
    }
  }

  @override
  void initState() {
    super.initState();
    if (widget.imageUrl != null && widget.imageUrl!.endsWith('pdf')) {
      _loadPdf();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(onTap: widget.onTap, child: _buildImageOrPdfView(context));
  }

  Widget _buildImageOrPdfView(BuildContext context) {
    if (widget.imageUrl != null && widget.imageUrl!.endsWith('pdf')) {
      return ValueListenableBuilder<bool>(
        valueListenable: _loadingPdf,
        builder: (_, loading, __) {
          
          if (loading) {
            return _buildLoadingView();
          }

          return ValueListenableBuilder<File?>(
            valueListenable: _pdfFile,
            builder: (_, file, __){
              if (file == null){
                return _buildErrorView('Erro ao carregar');
              }
              return _buildPdf(file);
            },
          );
        },
      );
    } else {
      return _buildImageView();
    }
  }

  Widget _buildLoadingView() {
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildErrorView(String message) {
    return Center(child: Text(message));
  }

  Widget _buildPdf(File file) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      width: MediaQuery.of(context).size.width * 0.8,
      alignment: Alignment.center,
      child: PDFView(filePath: file.path, onError: (error) => _buildErrorView(error.toString())),
    );
  }

  Widget _buildImageView() {
    return widget.imageUrl != null && widget.imageUrl!.isNotEmpty
        ? CachedNetworkImage(
          imageUrl: widget.imageUrl!,
          imageBuilder: (context, imageProvider) => Image(image: imageProvider, fit: BoxFit.contain, width: MediaQuery.of(context).size.width * 0.9),
          placeholder: (context, url) => const CircularProgressIndicator(),
          errorWidget: (context, url, error) => const Icon(Icons.error),
        )
        : Text(widget.textFoto, textAlign: TextAlign.center, style: const TextStyle(fontSize: 20, fontWeight: FontWeight.w500, fontFamily: 'Roboto'));
  }
}
