import 'package:asuka/asuka.dart' as asuka;
import 'package:dio/dio.dart';
// ignore: implementation_imports, depend_on_referenced_packages
import 'package:firebase_messaging_platform_interface/src/remote_message.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:latlong2/latlong.dart';
import 'package:octalog/src/helpers/web_connector.dart';
import 'package:octalog/src/utils/extesion.dart';
import 'package:map_fields/map_fields.dart';

import '../../../../errors.dart';
import '../../../database/firebase_background/firebase_background.dart';
import '../../../database/log_database/log_database.dart';
import '../../../helpers/gps/gps_contract.dart';
import '../../../models_new/position_data_location.dart';
import '../../../pages/home/<USER>';
import '../../../utils/colors.dart';
import '../../buttom_ls/button_ls_custom.dart';
import '../components/fcm_appbar_custom.dart';
import '../components/fcm_deslocamento_widget.dart';
import '../components/fcm_distance_data.dart';
import '../components/fcm_porcent.dart';
import '../fcm_alert_dialog_store.dart';

class UberizadoShowDailog extends StatefulWidget {
  final RemoteMessage message;
  final String? keyMessage;

  const UberizadoShowDailog({
    super.key,
    required this.message,
    this.keyMessage,
  });

  @override
  State<UberizadoShowDailog> createState() => _UberizadoShowDailogState();
}

class _UberizadoShowDailogState extends State<UberizadoShowDailog> {
  int get id => int.tryParse(widget.message.data['id'].toString()) ?? 0;
  String get titulo => widget.message.data['titulo'] ?? '';
  String get endereco => widget.message.data['endereco'] ?? '';
  double get latitude {
    final f = MapFields.load(widget.message.data);
    return f.getDouble('latitude', 0);
  }

  double get longitude {
    final f = MapFields.load(widget.message.data);
    return f.getDouble('longitude', 0);
  }

  double distancia = 0.0;
  LatLng origem = LatLng(0, 0);
  LatLng destino = LatLng(0, 0);

  String horario = '';
  bool loadingRecusar = false;
  late final FcmAlertDialogStore store;
  PositionDataLocation? posicao;
  bool isclinked = false;

  bool loadingAceitar = false;
  Future<void> init() async {
    final posicaoAtual =
        await GpsHelperContract.instance.updateAndGetLastPosition();
    posicao = posicaoAtual;
    try {
      final String hr = widget.message.data['horario'];
      final dt = DateTime.tryParse(hr);
      if (dt != null) {
        horario =
            '${dt.hour.toString().padLeft(2, '0')}:${dt.minute.toString().padLeft(2, '0')}';
      }
    } catch (_) {}
    setState(() {});
  }

  Future<void> calcularDistancia() async {
    final position =
        await GpsHelperContract.instance.updateAndGetLastPosition();
    origem = LatLng(position?.latitude ?? 0, position?.longitude ?? 0);
    destino = LatLng(latitude, longitude);
    try {
      final distancia = GeolocatorPlatform.instance.distanceBetween(
        position?.latitude ?? 0,
        position?.longitude ?? 0,
        latitude,
        longitude,
      );
      this.distancia = distancia;
    } catch (_) {}
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    calcularDistancia();
    init();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: Padding(
          padding: const EdgeInsets.only(
            left: 20,
            right: 20,
            top: 10,
          ),
          child: Column(
            children: [
              const FcmAppBarCustom(
                title: 'Alerta de Pedido',
                isBack: false,
              ),
              Padding(
                padding: const EdgeInsets.only(top: 18),
                child: Image.asset('assets/images/image_fmc.png'),
              ),
              const Padding(
                padding: EdgeInsets.only(top: 10, left: 10, right: 10),
                child: FcmPorcent(),
              ),
              const Padding(
                padding: EdgeInsets.all(10.0),
                child: Text(
                  'Você tem uma coleta para fazer na loja',
                  style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                      color: ColorsCustom.customOrange),
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              FcmDeslocamentoWidget(
                local: titulo,
                endereco: endereco,
              ),
              const SizedBox(
                height: 10,
              ),
              FcmDistanceData(
                horaFinal: null,
                destino: destino,
              ),
              Visibility(
                visible: horario.isNotEmpty,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 25),
                  child: Row(
                    children: [
                      Icon(
                        Icons.watch_later,
                        color: ColorsCustom.customOrange.withOpacity(0.8),
                      ),
                      const SizedBox(
                        width: 10,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(right: 30),
                        child: Text(horario),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(
                height: 20,
              ),
              const SizedBox(
                height: 50,
              )
            ],
          ),
        ),
        floatingActionButton: Padding(
          padding: const EdgeInsets.only(
            left: 40,
          ),
          child: Row(
            children: [
              Expanded(
                flex: 5,
                child: ButtonLsCustom(
                  isLoading: false,
                  text: 'RECUSAR',
                  colorBackground: ColorsCustom.customGrey,
                  onPressed: () async {
                    if (loadingAceitar) return;
                    loadingAceitar = true;
                    try {
                      await aceitarrecusar(false);
                      if (widget.keyMessage != null) {
                        await FcmDataBase.instance
                            .deleteFcm(widget.keyMessage!);
                      }
                    } catch (_) {}
                    loadingRecusar = false;
                  },
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                flex: 8,
                child: ButtonLsCustom(
                  isLoading: isclinked,
                  text: 'ACEITAR',
                  colorBackground: ColorsCustom.customOrange,
                  onPressed: isclinked
                      ? null
                      : () async {
                          if (loadingAceitar) return;
                          loadingAceitar = true;
                          await aceitarrecusar(true);
                          loadingAceitar = false;
                        },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> aceitarrecusar(bool condicao) async {
    try {
      final conn = WebConnector();
      final response = await conn.put(
        '/deslocamento/aceite',
        body: {
          "IDDeslocamento": id,
          "DataHora": DateTime.now().dataHoraServidorFomart.toIso8601String(),
          "Latitude": posicao?.latitude.toString(),
          "Longitude": posicao?.longitude.toString(),
          "Aceitou": condicao
        },
      );
      final f = MapFields.load(response.data);
      final retorno = f.getBool('retorno', false);
      if (condicao) {
        if (!retorno) {
          final nomeAgente = f.getString('NomeAgente', 'Outro entregador');
          await showDialog(
            context: context,
            builder: (ctx) {
              return AlertDialog(
                title: const Text('Aviso'),
                content: Text(
                  '$nomeAgente já aceitou o pedido.',
                ),
                actions: [
                  TextButton(
                    child: const Text(
                      'Ok',
                      style: TextStyle(color: Colors.orange),
                    ),
                    onPressed: () => Navigator.pop(ctx),
                  ),
                ],
              );
            },
          );
        }
      }
      // if (widget.keyMessage != null) {
      //   await FcmDataBase.instance.deleteFcm(widget.keyMessage!);
      // }
      HomeController.instance.fetchAtividades();
      Navigator.of(context).pop();
    } catch (e) {
      if (e is DioException) {
        await LogDatabase.instance.logError(
          '',
          '/deslocamento/aceite',
          'Aceitar deslocamento',
          {
            'error': e.toString(),
            'error_type': e.runtimeType.toString(),
            'error_response': e.response?.data.toString(),
            'status_code': e.response?.statusCode,
            'message': e.message,
          },
        );
      } else if (e is ConnectionError) {
        await LogDatabase.instance.logError(
          '',
          '/deslocamento/aceite',
          'Aceitar deslocamento',
          {
            'error': e.toString(),
            'error_type': e.runtimeType.toString(),
            'error_response': e.error,
            'status_code': e.status,
            'message': e.response,
          },
        );
      } else {
        await LogDatabase.instance.logError(
          '',
          '/deslocamento/aceite',
          'Aceitar deslocamento',
          {
            'error': e.toString(),
            'error_type': e.runtimeType.toString(),
          },
        );
      }
      asuka.AsukaSnackbar.warning(
        'Ocorreu um erro ao aceitar, verifique sua internet e tente novamente!',
      ).show();
    } finally {
      loadingAceitar = false;
    }
  }
}
