class ConnectionError {
  final int status;
  final String response;
  final String error;
  final String endpoint;

  ConnectionError(
    this.status,
    this.response,
    this.error,
    this.endpoint,
  );

  @override
  String toString() {
    String result = '($status) - $endpoint: ';
    if (status >= 100 && status <= 199) {
      result += 'Informação';
    } else if (status >= 200 && status <= 299) {
      result += 'Sucesso';
    } else if (status >= 300 && status <= 399) {
      result += 'Redirecionamento';
    } else if (status >= 400 && status <= 499) {
      result += 'Erro do cliente';
    } else if (status >= 500 && status <= 599) {
      result += 'Erro do servidor';
    } else {
      result += 'Erro desconhecido';
    }
    result += '\n$response';
    return result;
  }
}
