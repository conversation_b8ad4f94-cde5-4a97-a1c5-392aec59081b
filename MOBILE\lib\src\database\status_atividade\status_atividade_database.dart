import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';

import '../../helpers/web_connector.dart';
import '../../models/status_atividades.dart';
import 'status_atividade_model.dart';

class StatusAtividadeDatabase {
  static final StatusAtividadeDatabase instance = StatusAtividadeDatabase();
  late Box<String> _base;
  bool _initialized = false;

  StatusAtividadeDatabase() {
    _init();
  }

  Future<void> _init() async {
    if (!_initialized) {
      _base = await Hive.openBox('statusatividade_map');
      _initialized = true;
    }
  }

  Future<StatusAtividades?> getStatus() async {
    await _init();
    final data = _base.get('statusatividade_dados');
    if (data == null) {
      return null;
    }
    final status = StatusAtividadesModelHive.fromHiveMap(
      json.decode(data),
    ).toStatusAtividade();
    return status;
  }

  Future<StatusAtividadesModelHive?> fetchStatus() async {
    await _init();
    try {
      final responseStatus = await WebConnector().get(
        '/atividades/statusatividade',
      );
      final status = StatusAtividadesModelHive.fromJson(
        responseStatus.data,
      );
      final data = json.encode(status.toHiveMap());
      await _base.put('statusatividade_dados', data);
      return status;
    } catch (e) {
      debugPrint(e.toString());
      return null;
    }
  }
}
