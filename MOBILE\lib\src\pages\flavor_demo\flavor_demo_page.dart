import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../components/flavor_image/flavor_image.dart';
import '../../config/flavor_config.dart';
import '../../utils/theme_colors.dart';

/// Página de demonstração do sistema de flavors
/// Esta página mostra como usar corretamente o sistema de flavors
class FlavorDemoPage extends StatelessWidget {
  const FlavorDemoPage({super.key});

  @override
  Widget build(BuildContext context) {
    final config = FlavorConfig.instance;
    
    return Scaffold(
      appBar: AppBar(
        title: Text('Demo - ${config.name}'),
        backgroundColor: context.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Informações do Flavor
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Informações do Flavor',
                      style: GoogleFonts.roboto(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: context.customBlack,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildInfoRow('Nome:', config.name),
                    _buildInfoRow('Application ID:', config.applicationId),
                    _buildInfoRow('Flavor:', config.flavor.name),
                    _buildInfoRow('Asset Path:', config.assetPath),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Demonstração de Cores
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Cores do Tema',
                      style: GoogleFonts.roboto(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: context.customBlack,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        _buildColorChip('Primary', context.primaryColor),
                        _buildColorChip('Secondary', context.secondaryColor),
                        _buildColorChip('Custom Orange', context.customOrange),
                        _buildColorChip('Custom Green', context.customGreen),
                        _buildColorChip('Custom Red', context.customRed),
                        _buildColorChip('Custom Grey', context.customGrey),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Demonstração de Assets
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Assets do Flavor',
                      style: GoogleFonts.roboto(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: context.customBlack,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Column(
                          children: [
                            const FlavorLogo(width: 80, height: 80),
                            const SizedBox(height: 8),
                            Text(
                              'Logo Principal',
                              style: GoogleFonts.roboto(fontSize: 12),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                color: context.primaryColor,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: const FlavorLogoWhite(width: 60, height: 60),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Logo Branco',
                              style: GoogleFonts.roboto(fontSize: 12),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Demonstração de Botões
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Componentes com Tema',
                      style: GoogleFonts.roboto(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: context.customBlack,
                      ),
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {},
                        child: const Text('Botão Primary'),
                      ),
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton(
                        onPressed: () {},
                        child: const Text('Botão Outlined'),
                      ),
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      width: double.infinity,
                      child: TextButton(
                        onPressed: () {},
                        child: const Text('Botão Text'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: GoogleFonts.roboto(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.roboto(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildColorChip(String label, Color color) {
    return Chip(
      avatar: CircleAvatar(
        backgroundColor: color,
        radius: 12,
      ),
      label: Text(
        label,
        style: GoogleFonts.roboto(fontSize: 12),
      ),
    );
  }
}
