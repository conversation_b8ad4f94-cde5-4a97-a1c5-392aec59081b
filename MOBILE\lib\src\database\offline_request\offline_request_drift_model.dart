import 'dart:io';

import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';

part 'offline_request_drift_model.g.dart';

class OfflineRequestDrift extends Table {
  TextColumn get uuid => text().unique()();
  TextColumn get endpoint => text()();
  TextColumn get method => text()();
  TextColumn get body => text().nullable()();
  TextColumn get headers => text().nullable()();
  TextColumn get fileName => text().nullable()();
  BlobColumn get fileBytes => blob().nullable()();
  TextColumn get fileLink => text().nullable()();
  IntColumn get idAtividade => integer()();
  BoolColumn get enviado => boolean()();
  DateTimeColumn get dataHora => dateTime().nullable()();
  BoolColumn get isFcmDio => boolean().nullable()();
}

@DriftDatabase(tables: [OfflineRequestDrift])
class OfflineRequestDatabaseDrift extends _$OfflineRequestDatabaseDrift {
  static final OfflineRequestDatabaseDrift instance =
      OfflineRequestDatabaseDrift();

  OfflineRequestDatabaseDrift() : super(_openConnection());
  @override
  int get schemaVersion => 1;
}

LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(
      p.join(dbFolder.path, 'offline_request_db.sqlite'),
    );
    return NativeDatabase(file);
  });
}
