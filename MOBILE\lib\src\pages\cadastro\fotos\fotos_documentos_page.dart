import 'package:camera/camera.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:octalog/src/components/loading_ls/loading_ls.dart';
import 'package:octalog/src/pages/cadastro/components/card_fotos.dart';
import 'package:octalog/src/pages/cadastro/components/preview_pdf.dart';
import 'package:octalog/src/utils/colors.dart';
import 'package:permission_handler/permission_handler.dart';

import '../cadastro_state.dart';
import '../cadastro_store.dart';
import '../components/preview_image.dart';
import '../components/widget_camera_perfil.dart';
import '../enum/enum_page_fotos.dart';

class FotosPage extends StatefulWidget {
  final CadastroStore store;
  const FotosPage({super.key, required this.store});

  @override
  State<FotosPage> createState() => _FotosPageState();
}

class _FotosPageState extends State<FotosPage> {
  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<CadastroState>(
      valueListenable: widget.store.state,
      builder: (context, state, _) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              decoration: BoxDecoration(borderRadius: BorderRadius.circular(0), color: ColorsCustom.customGrey.withOpacity(0.2)),
              height: MediaQuery.of(context).size.height * 0.5,
              width: MediaQuery.of(context).size.width * 0.9,
              child: ValueListenableBuilder(
                valueListenable: widget.store.uploadingPdf,
                builder: (_, loading, __) {
                  if (loading) {
                    return Center(child: LoadingLs());
                  }
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Visibility(
                        visible: state.fotoPage == EnumCadastroFoto.fotoCnh,
                        child: CardFotos(
                          textFoto: 'Clique para selecionar a\nfoto da CNH',
                          imageUrl: state.contratoModel?.fotoCNH ?? '',
                          onTap: () async {
                            PlatformFile? file;
                            bool liberarFoto = false;
                            try {
                              file = await getImage();
                            } catch (e) {
                              pedirPermissaoParaAcessarFoto();
                            }
                            if (file != null) {
                              liberarFoto = await Navigator.push(context, MaterialPageRoute(builder: (context) => PreviewPdf(file: file!)));
                            }
                            if (liberarFoto) {
                              if (file!.extension!.contains('pdf')) {
                                widget.store.uploadPhoto('cnh', file.extension!, fileArquivo: file);
                              } else {
                                final XFile filefoto = XFile(file.path!);
                                widget.store.uploadPhoto('cnh', file.extension!, fileFoto: filefoto);
                              }
                            }
                          },
                        ),
                      ),
                      Visibility(
                        visible: state.fotoPage == EnumCadastroFoto.fotoPerfil,
                        child: CardFotos(
                          textFoto: 'Clique para selecionar a\nfoto do perfil',
                          imageUrl: state.contratoModel?.fotoUsuario ?? '',
                          onTap: () async {
                            try {
                              bool liberarFoto = false;
                              XFile? file = await Navigator.push(context, MaterialPageRoute(builder: (context) => const WidgetCameraPerfil()));
                              if (file != null) {
                                liberarFoto = await Navigator.push(context, MaterialPageRoute(builder: (context) => PreviewImage(imageFile: file)));
                              }
                              if (liberarFoto) {
                                widget.store.uploadPhoto('perfil', 'png', fileFoto: file!);
                              }
                            } catch (e) {
                              pedirPermissaoParaAcessarFoto();
                            }
                          },
                        ),
                      ),
                      Visibility(
                        visible: state.fotoPage == EnumCadastroFoto.comprovanteEndereco,
                        child: CardFotos(
                          textFoto: 'Clique para selecionar a\nfoto do comprovante de residência',
                          imageUrl: state.contratoModel?.fotoComprovanteEndereco ?? '',
                          onTap: () async {
                            PlatformFile? file;
                            bool liberarFoto = false;
                            try {
                              file = await getImage();
                            } catch (e) {
                              pedirPermissaoParaAcessarFoto();
                            }
                            if (file != null) {
                              liberarFoto = await Navigator.push(context, MaterialPageRoute(builder: (context) => PreviewPdf(file: file!)));
                            }
                            if (liberarFoto) {
                              if (file!.extension!.contains('pdf')) {
                                widget.store.uploadPhoto('comprovanteEndereco', file.extension!, fileArquivo: file);
                              } else {
                                final XFile filefoto = XFile(file.path!);
                                widget.store.uploadPhoto('comprovanteEndereco', file.extension!, fileFoto: filefoto);
                              }
                            }
                          },
                        ),
                      ),
                      Visibility(
                        visible: state.fotoPage == EnumCadastroFoto.documentoVeiculo,
                        child: CardFotos(
                          textFoto: 'Clique para selecionar a\nfoto do documento',
                          imageUrl: state.contratoModel?.fotoDocumentoVeiculo ?? '',
                          onTap: () async {
                            PlatformFile? file;
                            bool liberarFoto = false;
                            try {
                              file = await getImage();
                            } catch (e) {
                              pedirPermissaoParaAcessarFoto();
                            }
                            if (file != null) {
                              liberarFoto = await Navigator.push(context, MaterialPageRoute(builder: (context) => PreviewPdf(file: file!)));
                            }
                            if (liberarFoto) {
                              if (file!.extension!.contains('pdf')) {
                                widget.store.uploadPhoto('documentoVeiculo', file.extension!, fileArquivo: file);
                              } else {
                                final XFile filefoto = XFile(file.path!);
                                widget.store.uploadPhoto('documentoVeiculo', file.extension!, fileFoto: filefoto);
                              }
                            }
                          },
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
            const SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: List<Widget>.generate(EnumCadastroFoto.values.length, (index) {
                return _buildStep(
                  title: EnumCadastroFoto.values[index].name,
                  index: index,
                  isColor: state.fotoPage == EnumCadastroFoto.values[index],
                  onTap: () {
                    widget.store.setFotoPage(EnumCadastroFoto.values[index]);
                  },
                );
              }),
            ),
          ],
        );
      },
    );
  }

  Future<PlatformFile?> getImage() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles();

    if (result != null) {
      PlatformFile file = result.files.first;
      String fileName = file.extension!.toLowerCase();

      if (fileName != 'pdf' && fileName != 'png' && fileName != 'jpg' && fileName != 'jpeg' && fileName != 'heif') {
        return await showDialog(
          context: context,
          builder:
              (context) => AlertDialog(
                title: const Text('Erro'),
                content: const Text('Arquivo inválido. Arquivo deve ser: pdf, png, heif, jpg ou jpeg.'),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                      return;
                    },
                    child: const Text('Ok', style: TextStyle(color: ColorsCustom.customOrange)),
                  ),
                ],
              ),
        );
      }

      if (file.size > (2 * 1024 * 1024)) {
        return await showDialog(
          context: context,
          builder:
              (context) => AlertDialog(
                title: const Text('Erro'),
                content: const Text('Arquivo inválido, ultrapassa o limite de 2 megas.'),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                      return;
                    },
                    child: const Text('Ok', style: TextStyle(color: ColorsCustom.customOrange)),
                  ),
                ],
              ),
        );
      }

      return file;
    }
    return null;
  }

  Future pedirPermissaoParaAcessarFoto() async {
    PermissionStatus status = await Permission.photos.status;
    if (status == PermissionStatus.granted) {
      return true;
    } else {
      showDialog(
        context: context,
        builder:
            (context) => AlertDialog(
              title: const Text('Permissão'),
              content: const Text('Precisamos da sua permissão para acessar a galeria de fotos'),
              actions: [
                TextButton(
                  onPressed: () async {
                    Navigator.pop(context);
                    openAppSettings();
                  },
                  child: const Text('LIBERAR', style: TextStyle(color: ColorsCustom.customOrange)),
                ),
              ],
            ),
      );
    }
  }

  Widget _buildStep({required String title, required int index, required bool isColor, required void Function() onTap}) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(width: 5),
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(borderRadius: BorderRadius.circular(10), color: isColor ? ColorsCustom.customOrange : ColorsCustom.customGrey),
                  child: const Icon(Icons.photo_outlined, color: Colors.white),
                ),
                const SizedBox(width: 5),
              ],
            ),
            const SizedBox(height: 5),
            Text(title, textAlign: TextAlign.center, style: const TextStyle(fontSize: 12, fontFamily: 'Roboto', fontWeight: FontWeight.w500)),
          ],
        ),
      ),
    );
  }
}
