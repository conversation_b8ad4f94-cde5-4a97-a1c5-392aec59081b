import 'package:mailer/mailer.dart';
import 'package:mailer/smtp_server.dart';

abstract class SmtpEmailEnvio {
  static SmtpEmailEnvio instance = SmtpEmailEnvioImpl();
  Future<bool> enviarEmail({
    required List<String> emailsDestino,
    required String assunto,
    required String mensagem,
    required List<AnexoEmail> anexos,
  });
  Future<bool> enviarEmailSuporte({
    required String assunto,
    required String mensagem,
    required List<AnexoEmail> anexos,
  });
}

class SmtpEmailEnvioImpl implements SmtpEmailEnvio {
  static const String _username = '<EMAIL>';
  static const String _password = 'octalog.new';

  static final _smtpServer = SmtpServer(
    "smtp.office365.com",
    port: 587,
    username: _username,
    password: _password,
  );

  @override
  Future<bool> enviarEmail({
    required List<String> emailsDestino,
    required String assunto,
    required String mensagem,
    required List<AnexoEmail> anexos,
  }) async {
    try {
      final anexosFiles = anexos
          .map<Attachment>(
            (a) => StringAttachment(
              a.dados,
              fileName: a.nome,
            ),
          )
          .toList();
      final message = Message()
        ..from = const Address(_username, 'Octalog')
        ..recipients = emailsDestino
        ..subject = assunto
        ..text = mensagem
        ..attachments = anexosFiles;
      await send(message, _smtpServer);
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> enviarEmailSuporte({
    required String assunto,
    required String mensagem,
    required List<AnexoEmail> anexos,
  }) async {
    return await enviarEmail(
      anexos: anexos,
      assunto: assunto,
      mensagem: mensagem,
      emailsDestino: [
        '<EMAIL>',
      ],
    );
  }
}

class AnexoEmail {
  final String nome;
  final String dados;

  AnexoEmail({required this.nome, required this.dados});
}
