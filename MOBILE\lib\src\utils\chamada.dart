import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

void showContactOptions(BuildContext context, String phoneNumber) {
  phoneNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
  showModalBottomSheet(
    context: context,
    builder: (BuildContext context) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.phone),
            title: const Text('Ligaçao telefônica'),
            onTap: () async {
              String callUrl = 'tel:$phoneNumber';
              if (await canLaunchUrl(Uri.parse(callUrl))) {
                await launchUrl(Uri.parse(callUrl));
              } else {
                print('Não foi possível iniciar a ligação.');
              }
            },
          ),
          ListTile(
            leading: Image.asset('assets/images/whats.png', width: 28),
            title: const Text('Cha<PERSON> pelo WhatsApp'),
            onTap: () async {

              String formattedNumber = phoneNumber.replaceAll(RegExp(r'[^0-9]'), '');
              String urlWhats = "https://wa.me/55$formattedNumber";

              await launchUrl(Uri.parse(urlWhats), mode: LaunchMode.externalApplication);
            },
          ),
          const SizedBox(height: 50),
        ],
      );
    },
  );
}
