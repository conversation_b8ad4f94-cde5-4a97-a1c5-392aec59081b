import 'package:flutter/material.dart';
import 'package:octalog/src/components/buttom_ls/button_ls_custom.dart';
import 'package:octalog/src/components/custom_scaffold/custom_scaffold.dart';
import 'package:octalog/src/components/error_widget_custom/error_widget_custom.dart';
import 'package:octalog/src/components/fcm_alert_dailog/components/fcm_appbar_custom.dart';
import 'package:octalog/src/components/text_field_ls/text_field_ls_custom.dart';
import 'package:octalog/src/components/title_widget/title_widget.dart';
import 'package:octalog/src/pages/esqueceu_senha/components/verifique_celular.dart';
import 'package:octalog/src/pages/esqueceu_senha/esqueceu_senha_state.dart';
import 'package:octalog/src/pages/esqueceu_senha/esqueceu_senha_store.dart';

class EsqueceuSenha extends StatelessWidget {
  EsqueceuSenha({super.key});
  // final esqueceuSenhaBloc = EsqueceuSenhaBloc();
  final numeroController = TextEditingController();
  final store = EsqueceuSenhaStore();

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<EsqueceuSenhaState>(
        valueListenable: store.state,
        builder: (BuildContext context, value, Widget? child) {
          return CustomScaffold(
            isOffLineStack: false,
            child: SingleChildScrollView(
              child: ValueListenableBuilder(
                valueListenable: store.state,
                builder: (context, state, _) {
                  if (state.success) {
                    Future.delayed(const Duration(milliseconds: 100)).then(
                      (value) => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const VerifiqueCelular(),
                        ),
                      ),
                    );
                  }
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(10.0),
                        child: FcmAppBarCustom(
                          title: '',
                          isBack: true,
                          isLoadgin: false,
                          onPressed: () {},
                        ),
                      ),
                      const SizedBox(height: 150),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 25.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const TitleWidget(
                              title: 'Número de Celular',
                              subtitle:
                                  'Informe seu celular que enviaremos\num sms com o seu login e senha',
                            ),
                            if (value.error) const SizedBox(height: 40),
                            if (value.error)
                              const Center(
                                child: ErrorWidgetCustom(
                                  errorMessage:
                                      'Não encontramos este\nnúmero no nosso cadastro.\nEntre em contato com seu gestor',
                                ),
                              ),
                            const SizedBox(height: 40),
                            TextFieldLsCustom(
                              isError: value.error,
                              labelText: 'Número de celular',
                              controller: numeroController,
                              keyboardType: TextInputType.number,
                              isTelefone: true,
                            ),
                            const SizedBox(height: 30),
                            ButtonLsCustom(
                              isLoading: value.loading,
                              text: 'PRÓXIMA',
                              onPressed: () {
                                store.esqueceuSenha(numeroController.text);
                              },
                            ),
                          ],
                        ),
                      )
                    ],
                  );
                },
              ),
            ),
          );
        });
  }
}
