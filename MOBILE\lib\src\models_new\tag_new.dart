import 'package:flutter/material.dart';
import 'package:map_fields/map_fields.dart';

class TagNew {
  final String descricao;
  final String cor;
  TagNew({
    required this.descricao,
    required this.cor,
  });

  Map<String, dynamic> toHiveMap() {
    return {
      'descricao': descricao,
      'cor': cor,
    };
  }

  factory TagNew.fromHiveMap(Map<String, dynamic> map) {
    final d = MapFields.load(map);
    return TagNew(
      descricao: d.getString('descricao', ''),
      cor: d.getString('cor', ''),
    );
  }

  factory TagNew.fromJson(Map<String, dynamic> map) {
    final t = MapFields.load(map);
    return TagNew(
      descricao: t.getString('Descricao', ''),
      cor: t.getString('Cor', ''),
    );
  }

  Color getColor() {
    return Color(int.parse('0xFF$cor'));
  }
}
