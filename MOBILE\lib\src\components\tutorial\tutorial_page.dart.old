// webview_flutter:
import 'package:flutter/material.dart';
import 'package:octalog/src/components/custom_scaffold/custom_scaffold.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../database/config_blob/config_database.dart';

class TutorialPage extends StatefulWidget {
  const TutorialPage({super.key});

  @override
  State<TutorialPage> createState() => _TutorialPageState();
}

class _TutorialPageState extends State<TutorialPage> {
  String? url;

  @override
  void initState() {
    init();
    super.initState();
  }

  void init() async {
    await ConfigDatabase.instance.getConfig().then((value) {
      setState(() {
        url = value.linkTutorial;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      title: 'Tutorial',
      appBar: AppBar(
        title: const Text('Tutorial'),
      ),
      canPop: false,
      isColorIcon: true,
      onPopClose: () async {
        Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const TutorialPage()),
            (route) => false);

        return false;
      },
      onPop: () async {
        Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const TutorialPage()),
            (route) => false);

        return false;
      },
      child: WebView(
        initialUrl: url ?? 'https://octalogappcoleta.web.app/',
        javascriptMode: JavascriptMode.unrestricted,
        allowsInlineMediaPlayback: true,
        debuggingEnabled: true,
      ),
    );
  }
}
