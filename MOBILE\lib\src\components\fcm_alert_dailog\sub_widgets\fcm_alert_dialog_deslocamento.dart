import 'package:flutter/material.dart';
import 'package:octalog/src/components/fcm_alert_dailog/fcm_alert_dialog_store.dart';
import 'package:octalog/src/components/fcm_alert_dailog/models/fcm_deslocamento_get.dart';
import 'package:octalog/src/utils/extesion.dart';

import '../../../database/log_database/log_database.dart';
import '../../../pages/home/<USER>';
import '../../../utils/colors.dart';
import '../../buttom_ls/button_ls_custom.dart';
import '../components/fcm_appbar_custom.dart';
import '../components/fcm_deslocamento_widget.dart';
import '../components/fcm_distance_data.dart';
import '../components/fcm_porcent.dart';

class FcmAlertDailogDeslocamento extends StatefulWidget {
  final FcmAlertDialogStore store;
  const FcmAlertDailogDeslocamento({
    super.key,
    required this.store,
  });

  @override
  State<FcmAlertDailogDeslocamento> createState() =>
      _FcmAlertDailogDeslocamentoState();
}

class _FcmAlertDailogDeslocamentoState
    extends State<FcmAlertDailogDeslocamento> {
  FcmDeslocamentoGet get fcmAlertDados =>
      widget.store.state.value.fcmAlertDados;
  bool isclinked = false;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: SingleChildScrollView(
          reverse: true,
          child: Padding(
            padding: const EdgeInsets.only(left: 20, right: 20, top: 10),
            child: Column(
              children: [
                FcmAppBarCustom(
                  title: fcmAlertDados.titulo,
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 18),
                  child: Image.asset(
                    'assets/images/image_fmc.png',
                    width: MediaQuery.of(context).size.width * 0.9,
                  ),
                ),
                const Padding(
                  padding: EdgeInsets.only(top: 10, left: 10, right: 10),
                  child: FcmPorcent(),
                ),
                Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: !fcmAlertDados.reverso
                      ? const Text(
                          'Você tem uma coleta para fazer na loja',
                          style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                              color: ColorsCustom.customOrange),
                        )
                      : const Text(
                          'Você tem uma devolução para fazer na loja',
                          style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: ColorsCustom.customOrange),
                        ),
                ),
                const SizedBox(
                  height: 10,
                ),
                FcmDeslocamentoWidget(
                  local: fcmAlertDados.titulo,
                  endereco: fcmAlertDados.endereco,
                ),
                // const SizedBox(
                //   height: 10,
                // ),
                FcmDistanceData(
                  horaFinal: fcmAlertDados.reverso
                      ? null
                      : fcmAlertDados.horario.dataFormatada,
                  destino: fcmAlertDados.destino,
                ),
                const SizedBox(
                  height: 80,
                )
              ],
            ),
          ),
        ),
        floatingActionButton: Padding(
          padding: const EdgeInsets.only(
            left: 40,
            right: 10,
          ),
          child: ButtonLsCustom(
            isLoading: isclinked,
            text: 'DESLOCAMENTO À LOJA',
            colorBackground: ColorsCustom.customOrange,
            onPressed: () async {
              HomeController.instance.fetchAtividades();
              setState(() => isclinked = true);
              try {
                widget.store.inicio();
              } catch (e) {
                await LogDatabase.instance.logError(
                  '',
                  '/deslocamento/aceite',
                  'Aceitar deslocamento',
                  {
                    'error': e.toString(),
                    'error_type': e.runtimeType.toString(),
                  },
                );
                setState(() => isclinked = false);
              }
            },
          ),
        ),
      ),
    );
  }
}
