import 'package:map_fields/map_fields.dart';

import 'estados.dart';

class BuscaCepModel {
  String cep;
  String logradouro;
  String bairro;
  String cidade;
  String estado;
  double latitude;
  double longitude;
  String? uf;

  BuscaCepModel({
    required this.cep,
    required this.logradouro,
    required this.bairro,
    required this.cidade,
    required this.estado,
    required this.latitude,
    required this.longitude,
    required this.uf,
  });

  factory BuscaCepModel.fromJson(Map<String, dynamic> json) {
    final MapFields mapFields = MapFields.load(json);
   
    Estado? uf = estados.firstWhere(
      (element) => element.uf.contains(mapFields.getString('Estado', '')),
      orElse: () => Estado(uf: '', nome: 'Selecionar'),
    );

    return BuscaCepModel(
      cep: mapFields.getString('Cep', ''),
      logradouro: mapFields.getString('Logradouro', ''),
      bairro: mapFields.getString('Bairro', ''),
      cidade: mapFields.getString('Cidade', ''),
      estado: mapFields.getString('Estado', ''),
      latitude: mapFields.getDouble('Latitude', 0),
      longitude: mapFields.getDouble('Longitude', 0),
      uf: uf.uf,
    );
  }
}
