import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class IsOnlineWidget extends StatelessWidget {
  final bool isOnline;
  final Function(bool) onChanged;
  const IsOnlineWidget({
    super.key,
    required this.isOnline,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final icon = Container(
      width: 24,
      height: 24,
      color: isOnline ? Colors.green : Colors.grey,
      child: const Center(
        child: Icon(
          Icons.check,
          color: Colors.white,
          size: 18,
        ),
      ),
    );
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () async {
        await onChanged(!isOnline);
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(5),
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: isOnline ? Colors.green : Colors.grey,
            ),
            borderRadius: BorderRadius.circular(5),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              if (isOnline) icon,
              SizedBox(
                height: 22,
                child: Center(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: Text(
                      isOnline
                          ? 'Disponível para Coleta no Cliente'
                          : 'Indisponível para Coleta no Cliente',
                      style: GoogleFonts.roboto(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                        color: isOnline ? Colors.green : Colors.grey,
                      ),
                    ),
                  ),
                ),
              ),
              if (!isOnline) icon,
            ],
          ),
        ),
      ),
    );
  }
}
