import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:image_picker/image_picker.dart';
import 'package:octalog/src/components/loading_ls/loading_ls.dart';

import '../../../components/buttom_ls/button_ls_custom.dart';
import '../../../utils/colors.dart';

class PreviewImage extends StatefulWidget {
  final XFile imageFile;

  const PreviewImage({super.key, required this.imageFile});

  @override
  State<PreviewImage> createState() => _PreviewImageState();
}

class _PreviewImageState extends State<PreviewImage> {
  String? aviso;
  bool loadingTela = true;
  bool get isOs => Platform.isIOS;

  final faceDetector = FaceDetector(
      options: FaceDetectorOptions(
    enableClassification: true,
    enableLandmarks: true,
    enableContours: false,
    enableTracking: false,
  ));
  List<Face> faces = [];

  Future<void> init() async {
    final inputImage = InputImage.fromFilePath(widget.imageFile.path);
    faces = await faceDetector.processImage(inputImage);
    switch (faces.length) {
      case 0:
        aviso = "Nenhum rosto encontrado";
        log("Nenhum rosto encontrado");
        break;
      case 1:
        aviso = null;
        log("Um rosto encontrado");
        break;
      default:
        aviso = "Mais de um rosto encontrado";
        break;
    }
    await faceDetector.close();
    loadingTela = false;
    setState(() {});
  }

  @override
  void initState() {
    init();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: loadingTela
          ? const Center(
              child: LoadingLs(),
            )
          : Stack(
              children: [
                Transform(
                  alignment: Alignment.center,
                  transform: Matrix4.identity()..scale(-1.0, 1.0),
                  child: Image.file(
                    File(widget.imageFile.path),
                    fit: BoxFit.contain,
                    height: double.infinity,
                    width: double.infinity,
                    alignment: Alignment.center,
                  ),
                ),
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    color: Colors.black.withOpacity(0.5),
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                        Visibility(
                          visible: aviso != null,
                          child: Padding(
                            padding: const EdgeInsets.only(bottom: 20),
                            child: Text(
                              aviso != null ? aviso! : "",
                              textAlign: TextAlign.center,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            Expanded(
                              child: ButtonLsCustom(
                                text: 'TIRAR OUTRA',
                                colorBackground: ColorsCustom.customGrey,
                                onPressed: () {
                                  Navigator.pop(context, false);
                                },
                              ),
                            ),
                            Visibility(
                              visible: aviso == null || isOs,
                              child: const SizedBox(width: 20),
                            ),
                            Visibility(
                              visible: aviso == null || isOs,
                              child: Expanded(
                                child: ButtonLsCustom(
                                  text: 'GOSTEI',
                                  onPressed: () {
                                    Navigator.pop(context, true);
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
    );
  }
}
