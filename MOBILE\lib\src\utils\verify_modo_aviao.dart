import 'package:flutter/material.dart';
import 'package:octalog/src/helpers/airplane.dart';

import '../components/buttom_ls/button_ls_custom.dart';

class VerifiModoAviao extends StatefulWidget {
  const VerifiModoAviao({super.key});

  @override
  State<VerifiModoAviao> createState() => _VerifiModoAviaoState();
}

class _VerifiModoAviaoState extends State<VerifiModoAviao> {
  bool isLoad = false;
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          title: const Text("Modo Avião Ativado"),
          centerTitle: true,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Desative o modo avião e tente novamente!',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        floatingActionButton: Padding(
          padding: const EdgeInsets.only(
            left: 42,
            right: 20,
          ),
          child: ButtonLsCustom(
            text: "JÁ CORRIGI",
            isLoading: isLoad,
            onPressed: () async {
              final status = await Airplane.checkAirplaneMode();
              if (status.name != 'on') {
                if (Navigator.of(context).canPop()) {
                  Navigator.of(context).pop();
                }
              } else {
                setState(() {
                  isLoad = true;
                });
                await Future.delayed(const Duration(seconds: 1), () {
                  setState(() {
                    isLoad = false;
                  });
                });
              }
            },
          ),
        ),
      ),
    );
  }
}
