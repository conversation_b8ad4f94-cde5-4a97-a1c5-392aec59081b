import 'package:map_fields/map_fields.dart';

class RoterizarModel {
  double inicioLatitude;
  double inicioLongitude;
  double fimLatitude;
  double fimLongitude;

  RoterizarModel(
      {required this.inicioLatitude,
      required this.inicioLongitude,
      required this.fimLatitude,
      required this.fimLongitude});

  factory RoterizarModel.fromJson(Map<String, dynamic> json) {
    final MapFields r = MapFields.load(json);
    return RoterizarModel(
      // inicioLatitude: json['InicioLatitude'],
      // inicioLongitude: json['InicoLogitude'],
      // fimLatitude: json['FimLatitude'],
      // fimLongitude: json['FimLongitude']);
      inicioLatitude: r.getDouble('InicioLatitude', 0),
      inicioLongitude: r.getDouble('InicoLogitude', 0),
      fimLatitude: r.getDouble('FimLatitude', 0),
      fimLongitude: r.getDouble('FimLongitude', 0),
    );
  }
  Map<String, dynamic> toMap() {
    return {
      'InicioLatitude': inicioLatitude,
      'InicioLongitude': inicioLongitude,
      'FimLatitude': fimLatitude,
      'FimLongitude': fimLongitude,
    };
  }
}
