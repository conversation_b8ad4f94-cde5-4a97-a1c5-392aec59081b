import 'package:map_fields/map_fields.dart';

class HashBuscar {
  final int hash;
  final Map<String, dynamic> dados;
  HashBuscar({
    required this.hash,
    required this.dados,
  });

  factory HashBuscar.fromMap(Map<String, dynamic> map) {
    final f = MapFields.load(map);
    return HashBuscar(
      hash: f.getInt('hash', 0),
      dados: f.getMap<String, dynamic>('dados', {}),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'hash': hash,
      'dados': dados,
    };
  }
}
