// import 'dart:io';
import 'package:flutter/material.dart';
// import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:octalog/src/database/sac/sac_atendimento.dart';
import 'package:octalog/src/helpers/versoes_helper.dart';

// import 'package:octalog/src/helpers/versoes_helper.dart';

//import '../../components/fcm_alert_dailog/fcm_external/fcm_external_login.dart';
import '../../components/logo_ls/logo_ls_widget.dart';
import '../../database/config_blob/config_database.dart';
import '../login/login_page.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  void initFCM() async {
    try {
      await buscarUpdate();
      ConfigDatabase.instance.clear();
      await PedidosSacDatabase.instance.deleteAllSac();
    } catch (_) {}
    //await FmcExternalLogin.loadFCMKey();

    if (mounted) {
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const LoginPage()),
        (Route<dynamic> route) => false,
      );
    }
  }

  Future<void> buscarUpdate() async {
    await VersoesHelper.updateVerify(context);
    // try {
    //   FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    //       FlutterLocalNotificationsPlugin();
    //   if (Platform.isIOS) {
    //     await flutterLocalNotificationsPlugin
    //         .resolvePlatformSpecificImplementation<
    //             IOSFlutterLocalNotificationsPlugin>()
    //         ?.requestPermissions(
    //           alert: true,
    //           badge: true,
    //           sound: true,
    //         );
    //   }
    //   if (Platform.isAndroid) {
    //     await flutterLocalNotificationsPlugin
    //         .resolvePlatformSpecificImplementation<
    //             AndroidFlutterLocalNotificationsPlugin>()
    //         ?.requestNotificationsPermission();
    //   }
    // } catch (_) {}
  }

  @override
  void initState() {
    super.initState();
    initFCM();
  }

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      backgroundColor: Colors.white,
      body: Center(child: LogoLSWidget()),
    );
  }
}
