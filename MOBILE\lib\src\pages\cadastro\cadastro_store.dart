import 'dart:io';
import 'dart:math';
import 'dart:typed_data';

import 'package:asuka/asuka.dart' as asuka;
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:octalog/src/pages/cadastro/cadastro_state.dart';
import 'package:octalog/src/pages/cadastro/enum/enum_page_fotos.dart';
import 'package:path/path.dart';

import '../../../app_widget.dart';
import '../../../errors.dart';
import '../../helpers/login/login.dart';
import '../../helpers/login/login_hive.dart';
import '../../helpers/web_connector.dart';
import '../../models/busca_cep.dart';
import 'data/hive_contrato.dart';
import 'enum/enum_page_cadastro.dart';
import 'model/contrato_model.dart';
import 'model/model_veiculos.dart';

class CadastroStore {
  final ValueNotifier<CadastroState> state = ValueNotifier<CadastroState>(CadastroState.initial());
  final ValueNotifier<bool> uploadingPdf = ValueNotifier<bool>(false);

  void setTitlesPage(EnumCadastroPages value) {
    state.value = state.value.setTitlesPage(value);
  }

  void setFotoPage(EnumCadastroFoto value) {
    state.value = state.value.setFotoPage(value);
  }

  void setisLoadingcarregartela(bool value) {
    state.value = state.value.setisLoadingcarregartela(value);
  }

  void setPageController(PageController value) {
    state.value = state.value.setPageController(value);
  }

  void setContaCorrente(bool value) {
    state.value = state.value.setContaCorrente(value);
  }

  Future buscarContatrato(context) async {
    if (state.value.contratoModel?.nomeAgente != null && state.value.contratoModel!.nomeAgente.isNotEmpty) {
      return;
    }
    final connector = WebConnector();

    setisLoadingcarregartela(true);
    try {
      final responseVeiculos = await connector.get('/config/veiculos');

      final veiculos = Veiculo.fromJsonList(responseVeiculos.data);

      final responsecontrato = await connector.get('/agente/contrato-completo');

      final contrato = ContratoModel.fromJson(responsecontrato.data);

      setVeiculos(veiculos);
      setContratoModel(contrato);

      await ContratoPrefs.instance.save(contrato);
      // if (state.value.contratoModel.contrato == 1) {
      //   Navigator.pushAndRemoveUntil(
      //     context,
      //     MaterialPageRoute(
      //       builder: (context) => const FirstAccess(),
      //     ),
      //     (route) => false,
      //   );
      // }
      // setisLoadingcarregartela(false);
      setisLoadingcarregartela(false);
    } on ConnectionError catch (erro) {
      await asuka.Asuka.showDialog(
        barrierColor: Colors.black.withOpacity(0.8),
        barrierDismissible: false,
        builder:
            (context) => AlertDialog(
              backgroundColor: Colors.white,
              title: const Text('Erro no cadastro'),
              content: Text(erro.response),
              actions: [
                TextButton(
                  onPressed: () async {
                    await LoginHive.instance.clear();
                    await Login.instance.logout();

                    Navigator.pushAndRemoveUntil(context, MaterialPageRoute(builder: (context) => const AppWidget()), (route) => false);
                  },
                  child: const Text('OK'),
                ),
              ],
            ),
      );
    } catch (e) {
      setisLoadingcarregartela(false);
      debugPrint(e.toString());
    }
  }

  Future<bool> confimarContrato() async {
    final connector = WebConnector();
    setButtonAceiteContratoLoading(true);
    try {
      if (state.value.contaCorrente) {
        setContratoModelParte(pix: '', titularPix: '');
      } else {
        setContratoModelParte(banco: '', agencia: '', conta: '', titularConta: '');
      }

      final body = state.value.contratoModel!.toJson();
      await connector.post('/agente/aceite-contrato', body: body);
      setButtonAceiteContratoLoading(false);
      return true;
    } on ConnectionError catch (erro) {
      await asuka.Asuka.showDialog(
        barrierColor: Colors.black.withOpacity(0.8),
        barrierDismissible: false,
        builder:
            (context) => AlertDialog(
              backgroundColor: Colors.white,
              title: const Text('Erro no cadastro'),
              content: Text(erro.response),
              actions: [
                TextButton(
                  onPressed: () async {
                    Navigator.pop(context);
                  },
                  child: const Text('OK'),
                ),
              ],
            ),
      );
      setButtonAceiteContratoLoading(false);
      return false;
    } catch (e) {
      setButtonAceiteContratoLoading(false);
      return false;
    }
  }

  Future<void> uploadPhoto(String photoType, String contentType, {XFile? fileFoto, PlatformFile? fileArquivo}) async {
    Uint8List? bytes;
    String? filePath;
    uploadingPdf.value = true;

    try {
      if (fileFoto != null && fileArquivo?.extension.toString().toLowerCase() != 'pdf') {
        bytes = await fileFoto.readAsBytes();
        filePath = fileFoto.path;
      } else if (fileArquivo != null) {
        File file = File(fileArquivo.path!);
        bytes = await file.readAsBytes();
        filePath = fileArquivo.name;
      } else {
        throw Exception("No file provided for upload");
      }

      final connector = WebConnector();
      final rand = Random().nextInt(1000000);
      final fileNameBase = basename(filePath);
      final fileName = '${DateTime.now().millisecondsSinceEpoch}_$rand${extension(fileNameBase)}';
      String? photo;
      if (fileArquivo != null) {
        photo = await connector.postPdfBlobStorage(fileName: fileName, content: bytes);
      } else {
        photo = await connector.postImageBlobStorage(fileName: fileName, contentType: contentType, content: bytes, fotoAgente: true);
      }
      switch (photoType) {
        case 'perfil':
          setContratoModelParte(fotoUsuario: photo);
          break;
        case 'cnh':
          setContratoModelParte(fotoCNH: photo);
          break;
        case 'documentoVeiculo':
          setContratoModelParte(fotoDocumentoVeiculo: photo);
          break;
        case 'comprovanteEndereco':
          setContratoModelParte(fotoComprovanteEndereco: photo);
          break;
        default:
          throw Exception("Unsupported photo type: $photoType");
      }
    } catch (e) {
      rethrow;
    } finally {
      uploadingPdf.value = false;
    }
  }

  void setButtonAceiteContratoLoading(bool value) {
    state.value = state.value.setButtonAceiteContratoLoading(value);
  }

  void setContratoModel(ContratoModel value) {
    state.value = state.value.setContratoModel(value);
  }

  void setVeiculos(List<Veiculo> value) {
    state.value = state.value.setVeiculos(value);
  }

  String? validarAceiteTransportadora() {
    return null;
  }

  String? validarDadosPessoais() {
    final contrato = state.value.contratoModel;
    if (contrato!.nomeAgente.length < 3) {
      return 'Você precisa informar o nome para continuar';
    }
    if (contrato.cpf.length < 10) {
      return 'Você precisa informar o CPF para continuar';
    }
    if (contrato.telefone.length < 11) {
      return 'Você precisa informar o telefone para continuar';
    }

    if (!contrato.email.contains('@') && !contrato.email.contains('.') && !contrato.email.contains('com')) {
      return 'Você precisa informar o email para continuar';
    }
    return null;
  }

  String? validarEndereco() {
    final contrato = state.value.contratoModel;
    if (contrato!.cep.length < 8) {
      return 'Você precisa informar o CEP para continuar';
    }
    if (contrato.endereco.length < 3) {
      return 'Você precisa informar o Endereço para continuar';
    }
    if (contrato.numero == 0 || contrato.numero.toString().isEmpty) {
      return 'Você precisa informar o Número para continuar';
    }
    if (contrato.bairro.length < 5) {
      return 'Você precisa informar o Bairro para continuar';
    }
    if (contrato.cidade.length < 3) {
      return 'Você precisa informar a Cidade para continuar';
    }
    if (contrato.uf.length < 2) {
      return 'Você precisa informar o Estado para continuar';
    }
    return null;
  }

  String? validarTipoDataVeiculo() {
    final contrato = state.value.contratoModel;
    if (contrato!.iDTipoVeiculo == 0) {
      return 'Você precisa selecionar o tipo de veículo para continuar';
    }
    if (contrato.dataValidadeCNH == null) {
      return 'Você precisa informar a validade da CNH para continuar';
    }
    return null;
  }

  String? validarFotoCNH() {
    final contrato = state.value.contratoModel;
    if (contrato!.fotoCNH == null) {
      return 'Você precisa selecionar uma foto ou PDF da CNH para continuar';
    }
    return null;
  }

  String? validarDocumentoVeiculo() {
    final contrato = state.value.contratoModel;
    if (contrato!.fotoDocumentoVeiculo == null) {
      return 'Você precisa selecionar uma foto ou PDF do documento do veículo para continuar';
    }

    return null;
  }

  String? validarComprovanteEndereco() {
    final contrato = state.value.contratoModel;
    if (contrato!.fotoComprovanteEndereco == null) {
      return 'Você precisa selecionar uma foto ou PDF do seu comprovante de residência para continuar';
    }

    return null;
  }

  String? validarFotoPerfil() {
    final contrato = state.value.contratoModel;
    if (contrato!.fotoUsuario == null) {
      return 'Você precisa selecionar uma foto do perfil para continuar';
    }
    return null;
  }

  // pegar todas as validações e retornar uma
  String? validarAll() {
    String? validarDadosPessoais = this.validarDadosPessoais();
    String? validarEndereco = this.validarEndereco();
    String? validarFotoCNH = this.validarFotoCNH();
    String? validarDocumentoVeiculo = this.validarDocumentoVeiculo();
    String? validarFotoPerfil = this.validarFotoPerfil();
    String? validarTipoDataVeiculo = this.validarTipoDataVeiculo();

    return validarDadosPessoais ?? validarEndereco ?? validarFotoCNH ?? validarDocumentoVeiculo ?? validarTipoDataVeiculo ?? validarFotoPerfil;
  }

  void setisLoadingBuscaCep(bool value) {
    state.value = state.value.setisLoadingBuscaCep(value);
  }

  Future buscarCep() async {
    final cep = state.value.contratoModel!.cep;
    setisLoadingBuscaCep(true);
    try {
      final responseStatus = await WebConnector().get('/servicos/buscacep', queryParameters: {'cep': cep.replaceAll('-', '')});
      final cepBuscar = BuscaCepModel.fromJson(responseStatus.data);

      setContratoModelParte(endereco: cepBuscar.logradouro, bairro: cepBuscar.bairro, cidade: cepBuscar.cidade, uf: cepBuscar.uf);

      setisLoadingBuscaCep(false);
    } catch (e) {
      asuka.Asuka.showSnackBar(const SnackBar(content: Text('Erro ao buscar CEP', style: TextStyle(color: Colors.white))));
      setisLoadingBuscaCep(false);
      debugPrint(e.toString());
    }
  }

  List<EnumCadastroPages> getCadastroPages() {
    final contrato = state.value.contratoModel;

    if (contrato!.agentePrincipal) {
      return EnumCadastroPages.values;
    } else {
      return EnumCadastroPages.values.where((page) => page != EnumCadastroPages.dadosBancarios).toList();
    }
  }

  ContratoModel setContratoModelParte({
    int? idTipoAgente,
    String? tipoAgente,
    String? prestador,
    String? contrato,
    bool? prestadorAtivo,
    String? nomeAgente,
    String? cpf,
    String? telefone,
    String? email,
    String? cep,
    String? endereco,
    String? bairro,
    String? cidade,
    String? numero,
    String? uf,
    String? fotoUsuario,
    String? fotoCNH,
    String? fotoDocumentoVeiculo,
    String? fotoComprovanteEndereco,
    int? veiculo,
    int? idTipoVeiculo,
    DateTime? dataValidadeCNH,
    DateTime? dataConfirmouCadastro,
    String? banco,
    String? agencia,
    String? conta,
    String? titularConta,
    String? cpfCnpj,
    String? titularPix,
    String? pix,
  }) {
    final cont = ContratoModel(
      idTipoAgente: idTipoAgente ?? state.value.contratoModel!.idTipoAgente,
      tipoAgente: tipoAgente ?? state.value.contratoModel!.tipoAgente,
      prestador: prestador ?? state.value.contratoModel!.prestador,
      contrato: contrato ?? state.value.contratoModel!.contrato,
      prestadorAtivo: prestadorAtivo ?? state.value.contratoModel!.prestadorAtivo,
      nomeAgente: nomeAgente ?? state.value.contratoModel!.nomeAgente,
      cpf: cpf ?? state.value.contratoModel!.cpf,
      telefone: telefone ?? state.value.contratoModel!.telefone,
      email: email ?? state.value.contratoModel!.email,
      cep: cep ?? state.value.contratoModel!.cep,
      endereco: endereco ?? state.value.contratoModel!.endereco,
      bairro: bairro ?? state.value.contratoModel!.bairro,
      cidade: cidade ?? state.value.contratoModel!.cidade,
      numero: numero ?? state.value.contratoModel!.numero,
      uf: uf ?? state.value.contratoModel!.uf,
      fotoUsuario: fotoUsuario ?? state.value.contratoModel!.fotoUsuario,
      fotoCNH: fotoCNH ?? state.value.contratoModel!.fotoCNH,
      fotoDocumentoVeiculo: fotoDocumentoVeiculo ?? state.value.contratoModel!.fotoDocumentoVeiculo,
      fotoComprovanteEndereco: fotoComprovanteEndereco ?? state.value.contratoModel!.fotoComprovanteEndereco,
      iDTipoVeiculo: idTipoVeiculo ?? state.value.contratoModel!.iDTipoVeiculo,
      dataValidadeCNH: dataValidadeCNH ?? state.value.contratoModel!.dataValidadeCNH,
      dataConfirmouCadastro: dataConfirmouCadastro ?? state.value.contratoModel!.dataConfirmouCadastro,
      agentePrincipal: state.value.contratoModel!.agentePrincipal,
      banco: banco ?? state.value.contratoModel!.banco,
      agencia: agencia ?? state.value.contratoModel!.agencia,
      conta: conta ?? state.value.contratoModel!.conta,
      pix: pix ?? state.value.contratoModel!.pix,
      titularConta: titularConta ?? state.value.contratoModel!.titularConta,
      titularPix: titularPix ?? state.value.contratoModel!.titularPix,
    );
    state.value = state.value.setContratoModelParte(cont);
    return cont;
  }

  String? validarDadosBancarios() {
    final agentePrincipal = state.value.contratoModel!.agentePrincipal;

    if (agentePrincipal) {
      if (state.value.contaCorrente) {
        final contrato = state.value.contratoModel;
        if (contrato!.banco.length < 3) {
          return 'Você precisa informar o banco para continuar';
        }
        if (contrato.agencia.length < 3) {
          return 'Você precisa informar a agência para continuar';
        }
        if (contrato.conta.length < 3) {
          return 'Você precisa informar a conta para continuar';
        }
        if (contrato.titularConta.length < 3) {
          return 'Você precisa informar o titular da conta para continuar';
        }
      } else {
        final contrato = state.value.contratoModel;
        if (contrato!.pix.length < 3) {
          return 'Você precisa informar o PIX para continuar';
        }
        if (contrato.titularPix.length < 3) {
          return 'Você precisa informar o titular do PIX para continuar';
        }
      }
      if (!state.value.contaCorrente) {
        final contrato = state.value.contratoModel;
        if (contrato!.pix.length < 3) {
          return 'Você precisa informar o PIX para continuar';
        }
        if (contrato.titularPix.length < 3) {
          return 'Você precisa informar o titular do PIX para continuar';
        }
      }
    }
    return null;
  }
}
