import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

class MapaPageState {
  final MapController mapaPageController;
  final bool isTracking;
  final LatLng latLng;

  MapaPageState({
    required this.mapaPageController,
    required this.isTracking,
    required this.latLng,
  });

  MapaPageState copyWith({
    MapController? mapaPageController,
    bool? isTracking,
    LatLng? latLng,
  }) {
    return MapaPageState(
      mapaPageController: mapaPageController ?? this.mapaPageController,
      isTracking: isTracking ?? this.isTracking,
      latLng: latLng ?? this.latLng,
    );
  }
}
