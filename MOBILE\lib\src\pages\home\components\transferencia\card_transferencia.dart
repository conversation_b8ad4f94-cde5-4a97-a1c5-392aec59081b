import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:octalog/src/pages/home/<USER>/transferencia/lojas_model.dart';
import 'package:octalog/src/pages/home/<USER>';
import 'package:octalog/src/utils/colors.dart';

class CardLojasTransferencia extends StatefulWidget {
  final bool destino;
  List<LojasTransferencia> lojas;
  final LojasTransferencia? loja_selecionada;
  final HomeController controller;

  CardLojasTransferencia({
    super.key,
    required this.destino,
    required this.lojas,
    this.loja_selecionada,
    required this.controller,
    LojasTransferencia? origemSelecionada,
  });

  @override
  State<CardLojasTransferencia> createState() => _CardPedidoPadraomState();
}

class _CardPedidoPadraomState extends State<CardLojasTransferencia> {
  final _searchController = TextEditingController();
  List<LojasTransferencia> _filteredLojas = [];

  @override
  void initState() {
    super.initState();
    _filteredLojas = widget.lojas;

    _searchController.addListener(_filterLojas);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterLojas() {
    var query = _searchController.text.toLowerCase();
    setState(() {
      _filteredLojas = widget.lojas.where((loja) {
        return loja.loja.toLowerCase().contains(query);
      }).toList();
    });
  }

  @override
  void didUpdateWidget(covariant CardLojasTransferencia oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.lojas != widget.lojas) {
      _filteredLojas = widget.lojas;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      color: ColorsCustom.customBlue,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(8)),
        side: BorderSide(width: 2, color: ColorsCustom.customOrange),
      ),
      child: Padding(
        padding: const EdgeInsets.all(15.0),
        child: Column(
          children: [
            Row(
              children: [
                Text(
                  widget.destino
                      ? 'Loja de destino da transferência'
                      : 'Loja de inicio da transferência',
                  style: const TextStyle(
                      fontSize: 16, fontWeight: FontWeight.w500),
                ),
              ],
            ),
            if (widget.destino)
              KeyboardVisibilityBuilder(
                builder: (context, isKeyboardVisible) {
                  return Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: const Color.fromARGB(255, 255, 255, 255),
                      ),
                      child: TextField(
                        controller: _searchController,
                        decoration: InputDecoration(
                          labelText: 'Pesquisar clientes',
                          floatingLabelBehavior: FloatingLabelBehavior.never,
                          border: const OutlineInputBorder(
                              borderSide: BorderSide.none,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(8))),
                          prefixIcon: const Icon(Icons.search,
                              color: ColorsCustom.customOrange),
                          suffixIcon: !isKeyboardVisible
                              ? null
                              : Container(
                                  margin: const EdgeInsets.only(right: 10),
                                  width: 10,
                                  height: 10,
                                  child: GestureDetector(
                                    onTap: () => FocusScope.of(context)
                                        .requestFocus(FocusNode()),
                                    child: Image.asset(
                                        'assets/images/keyboard_close.png',
                                        color: Colors.black54),
                                  ),
                                ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _filteredLojas.length, // Use _filteredLojas here
              itemBuilder: (context, index) {
                final loja = _filteredLojas[index];
                return ListTile(
                    title: Text(
                      _filteredLojas[index].loja,
                      style: const TextStyle(
                          fontSize: 16, fontWeight: FontWeight.w600),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(_filteredLojas[index].endereco),
                        Row(
                          children: [
                            Image.asset('assets/images/locations.png',
                                width: 23, color: ColorsCustom.customOrange),
                            const Text(' Distancia:'),
                            _filteredLojas[index].distanciaKM != 0
                                ? Text(_filteredLojas[index]
                                    .distanciaKM
                                    .toString())
                                : const SizedBox(),
                            const Text(' km'),
                          ],
                        ),
                      ],
                    ),
                    onTap: () {
                      setState(() {
                        if (widget.destino) {
                          if (widget.loja_selecionada?.idLoja == loja.idLoja) {
                            widget.controller.setNullTransferencia(
                              isLojaDestino: true,
                            );
                          } else {
                            widget.controller.setLojaDestino(loja);
                          }
                        } else {
                          if (widget.loja_selecionada?.idLoja == loja.idLoja) {
                            widget.controller.setNullTransferencia(
                              isLojaOrigem: true,
                            );
                          } else {
                            widget.controller.setLojaOrigem(loja);
                          }
                        }
                      });
                    },
                    leading: Visibility(
                      visible: _filteredLojas.length > 1,
                      child: _filteredLojas[index].idLoja ==
                              widget.loja_selecionada?.idLoja
                          ? Column(
                              children: [
                                Container(
                                  width: 20,
                                  height: 20,
                                  decoration: const BoxDecoration(
                                    color: ColorsCustom.customOrange,
                                    shape: BoxShape.circle,
                                  ),
                                ),
                              ],
                            )
                          : Column(
                              children: [
                                Container(
                                  width: 20,
                                  height: 20,
                                  decoration: const BoxDecoration(
                                    color: Color.fromARGB(255, 255, 255, 255),
                                    shape: BoxShape.circle,
                                  ),
                                ),
                              ],
                            ),
                    ));
              },
            ),
          ],
        ),
      ),
    );
  }
}
