<variant
    name="release"
    package="dev.fluttercommunity.android_id"
    minSdkVersion="21"
    targetSdkVersion="21"
    mergedManifest="C:\projetos\octa.log\MOBILE\build\android_id\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml"
    proguardFiles="C:\projetos\octa.log\MOBILE\build\android_id\intermediates\default_proguard_files\global\proguard-android.txt-8.7.0"
    partialResultsDir="C:\projetos\octa.log\MOBILE\build\android_id\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6acd1316a909a3b9467814415d8b5421\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\main\kotlin;src\release\java;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="C:\projetos\octa.log\MOBILE\build\android_id\intermediates\javac\release\compileReleaseJavaWithJavac\classes;C:\projetos\octa.log\MOBILE\build\android_id\tmp\kotlin-classes\release;C:\projetos\octa.log\MOBILE\build\android_id\intermediates\compile_r_class_jar\release\generateReleaseRFile\R.jar"
      type="MAIN"
      applicationId="dev.fluttercommunity.android_id"
      generatedSourceFolders="C:\projetos\octa.log\MOBILE\build\android_id\generated\ap_generated_sources\release\out"
      generatedResourceFolders="C:\projetos\octa.log\MOBILE\build\android_id\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6acd1316a909a3b9467814415d8b5421\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
