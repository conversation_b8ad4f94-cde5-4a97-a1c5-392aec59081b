// ignore_for_file: use_build_context_synchronously, deprecated_member_use

import 'package:asuka/asuka.dart' as asuka;
import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:google_fonts/google_fonts.dart';
// import 'package:octalog/src/components/buttom_ls/button_ls_custom.dart';
import 'package:octalog/src/components/text_field_ls/text_field_ls_custom.dart';
import 'package:octalog/src/pages/home/<USER>';
import 'package:octalog/src/pages/search/search_state.dart';
import 'package:octalog/src/pages/search/search_store.dart';

import '../../components/scan_page/scan_page.dart';
import '../../models/expedir_model.dart';
import '../../utils/colors.dart';
import '../entrega_newpages/componentes/bottom_sheet_codbarras.dart';
import '../home/<USER>/custom_enum_navigation.dart';

class Search extends StatefulWidget {
  final HomeController controller;

  const Search({
    super.key,
    required this.controller,
  });

  @override
  State<Search> createState() => _SearchState();
}

class _SearchState extends State<Search> {
  final searchStore = SearchStore();
  final controller = HomeController.instance;
  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<SearchState>(
        valueListenable: searchStore.state,
        builder: (BuildContext context, SearchState value, Widget? child) {
          searchStore.init();
          return KeyboardVisibilityBuilder(
            builder: (context, isKeyboardVisible) {
              return Column(
                children: [
                  Container(
                    color: ColorsCustom.customOrange, // Cor de fundo
                    height: 10, // Altura do SizedBox
                  ),
                  Container(
                    color: ColorsCustom.customOrange,
                    child: SizedBox(
                      height: 55,
                      width: double.infinity,
                      child: Row(
                        children: [
                          const SizedBox(width: 20),
                          Expanded(
                            child: 
                            
                            Container(
                              height: 60,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(0),
                              ),
                              child: 
                              
                              TextFieldLsCustom(
                                textInputAction: TextInputAction.search,
                                onSubmitted: (value) {
                                  if (value?.trim().isEmpty ?? true) return;
                                  searchStore.setSearch(
                                    value!,
                                    addToHistory: true,
                                  );
                                  widget.controller.setSearch(
                                      searchStore.state.value.search);
                                  widget.controller
                                      .setPageSelected(HomeNavigationEnum.home);
                                },
                                autoFocus: true,
                                suffixIcon: SizedBox(
                                  width: 90,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      IconButton(
                                        onPressed: () async {
                                          String? barcodeScanRes =
                                              await showDialog<String>(
                                            context: context,
                                            barrierDismissible: false,
                                            builder: (ctx) => const ScanPage(),
                                          );
                                          if (barcodeScanRes! != '-1') {
                                            searchStore.setSearch(
                                              barcodeScanRes,
                                              addToHistory: true,
                                            );

                                            await widget.controller
                                                .setSearch(barcodeScanRes);

                                            widget.controller
                                                .setIsBarcode(true);
                                            await Future.delayed(
                                                const Duration(seconds: 1),
                                                () {});
                                            widget.controller.setPageSelected(
                                                HomeNavigationEnum.home);
                                            await asuka.Asuka.showDialog(
                                              barrierDismissible: false,
                                              builder: (context) {
                                                return AlertDialog(
                                                  backgroundColor: Colors.white,
                                                  title: const Text(
                                                    'Você está com esse pedido?',
                                                    style:
                                                        TextStyle(fontSize: 22),
                                                  ),
                                                  content: Text(
                                                    'Confirme para expedir este pedido para o seu aplicativo.\nPedido: $barcodeScanRes',
                                                    style: const TextStyle(
                                                        fontSize: 16),
                                                  ),
                                                  actions: [
                                                    Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceBetween,
                                                      mainAxisSize:
                                                          MainAxisSize.max,
                                                      children: [
                                                        TextButton(
                                                          onPressed: () async {
                                                            Navigator.of(
                                                                    context)
                                                                .pop();
                                                            await controller
                                                                .fetchAtividades();
                                                            controller
                                                                .setIsBarcode(
                                                                    false);
                                                            await controller
                                                                .setSearch('');
                                                          },
                                                          child: Text(
                                                            'CANCELAR',
                                                            style: TextStyle(
                                                                color: Colors
                                                                    .grey
                                                                    .shade800),
                                                          ),
                                                        ),
                                                        ValueListenableBuilder(
                                                            valueListenable:
                                                                controller
                                                                    .state,
                                                            builder: (context,
                                                                state, _) {
                                                              return TextButton(
                                                                onPressed:
                                                                    () async {
                                                                  final resp =
                                                                      await controller
                                                                          .expedirPedido(
                                                                    state
                                                                        .search,
                                                                    null,
                                                                    true,
                                                                  );

                                                                  if (resp !=
                                                                          null &&
                                                                      resp.isNotEmpty) {
                                                                    await _codBarras(
                                                                      context,
                                                                      state
                                                                          .search,
                                                                      resp,
                                                                      true,
                                                                    );
                                                                  }
                                                                  controller
                                                                      .setSearch(
                                                                          '');
                                                                  controller
                                                                      .fetchAtividades();
                                                                  controller
                                                                      .setIsBarcode(
                                                                          false);
                                                                },
                                                                child:
                                                                    const Text(
                                                                  'CONFIRMAR',
                                                                  style: TextStyle(
                                                                      color: ColorsCustom
                                                                          .customOrange),
                                                                ),
                                                              );
                                                            }),
                                                      ],
                                                    ),
                                                  ],
                                                );
                                              },
                                            );
                                          } else {}
                                        },
                                        icon: const Icon(Icons.qr_code_scanner),
                                      ),
                                      if (isKeyboardVisible) ...[
                                        Container(
                                          width: 27,
                                          height: 27,
                                          alignment: Alignment.bottomCenter,
                                          child: GestureDetector(
                                            onTap: () {
                                              FocusScope.of(context)
                                                  .requestFocus(FocusNode());
                                            },
                                            child: Image.asset(
                                              'assets/images/keyboard_close.png',
                                              width: 25,
                                              color: Colors.black54,
                                            ),
                                          ),
                                        ),
                                        const SizedBox(
                                          width: 5,
                                        )
                                      ]
                                    ],
                                  ),
                                ),
                                isError: false,
                                labelText: 'pesquisar pedido',
                                preffixIcon: const Icon(Icons.search),
                                disableLine: true,
                                onChanged: (value) => searchStore.setSearch(
                                  value,
                                ),
                              ),
                            ),
                          
                          ),
                          const SizedBox(width: 20),
                        ],
                      ),
                    ),
                  ),
                  Container(
                    color: ColorsCustom.customOrange, // Cor de fundo
                    height: 3, // Altura do SizedBox
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 0),
                    child: Container(
                      color: ColorsCustom
                          .customOrange, // Set the desired background color here
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(
                                left: 20.0), // Add 20.0 pixels of left padding
                            child: 
                            Text(
                              '',
                              style: GoogleFonts.roboto(
                                fontSize: 15,
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              searchStore.clearHistory();
                            },
                            child: Padding(
                              padding: const EdgeInsets.only(
                                  right:
                                      20.0), // Add 20.0 pixels of left padding
                              child: Text(
                                'limpar histórico',
                                style: GoogleFonts.roboto(
                                  fontSize: 15,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.yellow,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Text(
                              'Pesquisas Recentes',
                              style: GoogleFonts.roboto(
                                fontSize: 20,
                                //color: Colors.white,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                  // Builder(
                  //   builder: (_) {
                  //     if (value.searchHistory.isEmpty) {
                  //       return Container();
                  //     }
                  //     return SizedBox(
                  //       width: 350,
                  //       child: Center(
                  //         child: ButtonLsCustom(
                  //             text: 'Exibir resultados para ${value.search}',
                  //             onPressed: () {
                  //               searchStore.setSearch(
                  //                 value.search,
                  //                 addToHistory: true,
                  //               );
                  //               widget.controller.setSearch(
                  //                 value.search,
                  //               );
                  //               widget.controller
                  //                   .setPageSelected(HomeNavigationEnum.home);
                  //             }),
                  //       ),
                  //     );
                  //   },
                  // ),
                  Expanded(
                    child: Builder(builder: (_) {
                      final searchHistory = value.searchHistory;
                      if (searchHistory.isEmpty) {
                        return Center(
                          child: Text(
                            'Histórico de pesquisa vazio!',
                            style: GoogleFonts.roboto(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        );
                      }
                      return ListView(
                        children: searchHistory.map((e) {
                          return Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 15,
                              vertical: 5,
                            ),
                            child: InkWell(
                              onTap: () {
                                widget.controller.setSearch(e);
                                widget.controller
                                    .setPageSelected(HomeNavigationEnum.home);
                                // Navigator.pop(context, {
                                //   'search': e,
                                //   'isBarcode': false,
                                // });
                              },
                              child: Row(
                                children: [
                                  Text(
                                    e,
                                    style: GoogleFonts.roboto(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  const Spacer(),
                                  const Icon(
                                    Icons.history,
                                    color: ColorsCustom.customGrey,
                                    size: 20,
                                  ),
                                ],
                              ),
                            ),
                          );
                        }).toList(),
                      );
                    }),
                  ),
                ],
              );
            },
          );
        });
  }

  _codBarras(
    context,
    String barcode,
    List<ExpedirModel> expedirModel,
    bool expedir,
  ) async {
    try {
      showModalBottomSheet(
        isDismissible: true,
        isScrollControlled: true,
        context: context,
        builder: (context) => ButtomSheetCodbaras(
          barcode: barcode,
          expedirModel: expedirModel,
          expedir: expedir,
        ),
      );
    } catch (_) {}
  }
}
