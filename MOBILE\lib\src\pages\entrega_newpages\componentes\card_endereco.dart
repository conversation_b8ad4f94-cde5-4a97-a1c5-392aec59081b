// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/pages/entrega_newpages/componentes/waze_google.dart';
import 'package:octalog/src/pages/entrega_newpages/ocorrencias/entrega_negativa.dart';

import '../../../utils/colors.dart';
import '../controller/entrega_new_etapa.dart';
import '../controller/entrega_new_state.dart';
import '../controller/entrega_new_store.dart';

class CardEndereco extends StatelessWidget {
  final EntregaNewStore store;
  final bool isColor;
  const CardEndereco({
    super.key,
    required this.store,
    this.isColor = false,
  });

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<EntregaNewState>(
        valueListenable: store.state,
        builder: (BuildContext context, EntregaNewState value, Widget? child) {
          final atividade = value.atividade;

          final informaNegativa = [
            EntregaNewEtapa.deslocando,
            EntregaNewEtapa.foto,
            EntregaNewEtapa.finalizar,
          ].contains(value.etapa);

          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            width: double.infinity,
            child: GestureDetector(
              onTap: () async {
                final exibir = store.filtrarStatusAtividadesChild(
                  indexClienteRestante: value.indexClienteEscolhido,
                );
                if (exibir!.isEmpty) return;
                if (!informaNegativa) return;
                bool infNegativa = true;
                if (isColor) {
                  infNegativa = await showDialog(
                    barrierDismissible: false,
                    context: context,
                    builder: (ctx) => AlertDialog(
                      title: const Text('Atenção'),
                      content:
                          const Text('Você não tirou foto deseja continuar?'),
                      actions: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            TextButton(
                              onPressed: () {
                                Navigator.pop(ctx, false);
                              },
                              child: Text(
                                'CANCELAR',
                                style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.grey.shade800),
                              ),
                            ),
                            TextButton(
                              onPressed: () {
                                Navigator.pop(ctx, true);
                              },
                              child: const Text(
                                'CONFIRMAR',
                                style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: ColorsCustom.customOrange),
                              ),
                            ),
                          ],
                        )
                      ],
                    ),
                  );
                }
                if (infNegativa) {
                  await showModalBottomSheet(
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(30),
                        topRight: Radius.circular(30),
                      ),
                    ),
                    isScrollControlled: true,
                    isDismissible: true,
                    context: context,
                    builder: (_) => EntregaNegativa(
                      store: store,
                      indexClienteRestante: value.indexClienteEscolhido,
                      isOcorrenciaGlobal:
                          (value.indexClienteEscolhido == null &&
                                  value.etapa == EntregaNewEtapa.finalizar) ||
                              value.etapa != EntregaNewEtapa.finalizar,
                      removerAcareacao: true,
                    ),
                  );
                }
              },
              child: Card(
                shadowColor: Colors.transparent,
                color: ColorsCustom.customGreyLight,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15.0),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Container(
                    margin: EdgeInsets.zero,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 14,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Visibility(
                            visible: atividade.enderecoCompleto.isNotEmpty &&
                                atividade.clientes.isNotEmpty,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Text(
                                  atividade.enderecoFormatado,
                                  style: GoogleFonts.roboto(
                                    fontSize: 20,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Stack(
                            children: [
                              Visibility(
                                  visible:
                                      value.etapa == EntregaNewEtapa.deslocando,
                                  child: WazeGoogle(atividade: atividade)),
                              Visibility(
                                // se for diferente de null e não for vazio
                                visible: store.filtrarStatusAtividadesChild(
                                          indexClienteRestante:
                                              value.indexClienteEscolhido,
                                        ) !=
                                        null &&
                                    store
                                        .filtrarStatusAtividadesChild(
                                          indexClienteRestante:
                                              value.indexClienteEscolhido,
                                        )!
                                        .isNotEmpty,
                                child: Visibility(
                                  visible: informaNegativa,
                                  child: Padding(
                                    padding: const EdgeInsets.only(top: 15),
                                    child: Center(
                                      child: Text(
                                        'Informar negativa',
                                        style: GoogleFonts.roboto(
                                          fontSize: 20,
                                          fontWeight: FontWeight.bold,
                                          color: ColorsCustom.customOrange,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        });
  }
}
