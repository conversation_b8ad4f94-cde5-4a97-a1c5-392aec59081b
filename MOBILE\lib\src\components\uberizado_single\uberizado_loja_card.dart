// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../components/image_perfil/image_perfil.dart';
import '../../utils/colors.dart';

class UberizadoLojaCard extends StatelessWidget {
  final String titulo;
  final String foto;
  final String iniciais;
  final String endereco;
  const UberizadoLojaCard({
    super.key,
    required this.titulo,
    required this.foto,
    required this.iniciais,
    required this.endereco,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 30, right: 30, top: 5),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                titulo.toUpperCase(),
                style: GoogleFonts.roboto(
                    color: ColorsCustom.customOrange,
                    fontSize: 18,
                    fontWeight: FontWeight.w500),
              ),
              const Sized<PERSON>ox(height: 6),
              Row(
                children: [
                  SizedBox(
                    height: 48,
                    width: 48,
                    child: ImagePerfil(
                      url: foto,
                      iniciais: iniciais,
                      fontSize: 25,
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(child: Text(endereco)),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 5),
        const Divider(
          color: ColorsCustom.customGrey,
          thickness: 1,
        ),
      ],
    );
  }
}
