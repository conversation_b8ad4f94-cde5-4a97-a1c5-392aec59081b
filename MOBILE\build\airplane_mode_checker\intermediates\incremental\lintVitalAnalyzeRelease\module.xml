<lint-module
    format="1"
    dir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\airplane_mode_checker-3.1.0\android"
    name=":airplane_mode_checker"
    type="LIBRARY"
    maven="com.u14h4i.airplane_mode_checker:airplane_mode_checker:1.0-SNAPSHOT"
    agpVersion="8.7.0"
    buildFolder="C:\projetos\octa.log\MOBILE\build\airplane_mode_checker"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\sdk\platforms\android-34\android.jar;C:\Users\<USER>\AppData\Local\Android\sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-34"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
