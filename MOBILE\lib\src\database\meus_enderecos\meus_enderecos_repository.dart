import 'package:octalog/src/helpers/login/login.dart';
import 'package:octalog/src/helpers/web_connector.dart';
import 'package:octalog/src/models/meus_enderecos_model.dart';

Future<List<MeusEnderecosModel>> getEndereco() async {
  // var meusEnderecoAPIUrl =
  //     Uri.parse('https://api-mobile-v2.azurewebsites.net/agente/meusenderecos');
  // var response = await http.get(meusEnderecoAPIUrl, headers: {
  //   'token': Login.instance.usuarioLogado?.token ?? '',
  // });

  var response = await WebConnector().get(
    '/agente/meusenderecos',
    headers: {
      'token': Login.instance.usuarioLogado?.token ?? '',
    },
  );

  if (response.statusCode == 200) {
    // var jsonResponse = json.decode(response.data) as List;
    // return jsonResponse
    //     .map((meuEndereco) => MeusEnderecosModel.fromJson(meuEndereco))
    //     .toList();

    return (response.data as List)
        .map((meuEndereco) => MeusEnderecosModel.fromJson(meuEndereco))
        .toList();
  } else {
    throw Exception('Failed to load get');
  }
}
