import 'package:flutter/material.dart';
import 'package:iconsax/iconsax.dart';

enum HomeNavigationEnum {
  home(
    title: 'home',
    icon: Icon(Iconsax.location),
  ),
  search(
    title: 'buscar',
    icon: Icon(Iconsax.search_zoom_in),
    coletaEnable: false,
  ),
  coletar(
    title: 'coletar',
    icon: Icon(Iconsax.task),
    coletaEnable: false,
  ),
  transferencia(
    title: 'Transferir',
    icon: ImageIcon(
      AssetImage("assets/images/transferir.png"),
      color: Colors.white70,
    ),
    workOffline: false,
  ),
  route(
    title: 'mapa',
    icon: Icon(Iconsax.shuffle),
    workOffline: false,
  ),
  routing(
    title: 'rota',
    icon: Icon(Iconsax.truck_tick),
    workOffline: false,
  );

  final String title;
  final Widget icon;
  final bool workOffline;
  final bool coletaEnable;

  const HomeNavigationEnum({
    required this.title,
    required this.icon,
    this.workOffline = true,
    this.coletaEnable = true,
  });
}
