// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'offline_request_drift_model.dart';

// ignore_for_file: type=lint
class $OfflineRequestDriftTable extends OfflineRequestDrift
    with TableInfo<$OfflineRequestDriftTable, OfflineRequestDriftData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $OfflineRequestDriftTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _uuidMeta = const VerificationMeta('uuid');
  @override
  late final GeneratedColumn<String> uuid = GeneratedColumn<String>(
      'uuid', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways('UNIQUE'));
  static const VerificationMeta _endpointMeta =
      const VerificationMeta('endpoint');
  @override
  late final GeneratedColumn<String> endpoint = GeneratedColumn<String>(
      'endpoint', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _methodMeta = const VerificationMeta('method');
  @override
  late final GeneratedColumn<String> method = GeneratedColumn<String>(
      'method', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _bodyMeta = const VerificationMeta('body');
  @override
  late final GeneratedColumn<String> body = GeneratedColumn<String>(
      'body', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _headersMeta =
      const VerificationMeta('headers');
  @override
  late final GeneratedColumn<String> headers = GeneratedColumn<String>(
      'headers', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _fileNameMeta =
      const VerificationMeta('fileName');
  @override
  late final GeneratedColumn<String> fileName = GeneratedColumn<String>(
      'file_name', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _fileBytesMeta =
      const VerificationMeta('fileBytes');
  @override
  late final GeneratedColumn<Uint8List> fileBytes = GeneratedColumn<Uint8List>(
      'file_bytes', aliasedName, true,
      type: DriftSqlType.blob, requiredDuringInsert: false);
  static const VerificationMeta _fileLinkMeta =
      const VerificationMeta('fileLink');
  @override
  late final GeneratedColumn<String> fileLink = GeneratedColumn<String>(
      'file_link', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _idAtividadeMeta =
      const VerificationMeta('idAtividade');
  @override
  late final GeneratedColumn<int> idAtividade = GeneratedColumn<int>(
      'id_atividade', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _enviadoMeta =
      const VerificationMeta('enviado');
  @override
  late final GeneratedColumn<bool> enviado = GeneratedColumn<bool>(
      'enviado', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: true,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("enviado" IN (0, 1))'));
  static const VerificationMeta _dataHoraMeta =
      const VerificationMeta('dataHora');
  @override
  late final GeneratedColumn<DateTime> dataHora = GeneratedColumn<DateTime>(
      'data_hora', aliasedName, true,
      type: DriftSqlType.dateTime, requiredDuringInsert: false);
  static const VerificationMeta _isFcmDioMeta =
      const VerificationMeta('isFcmDio');
  @override
  late final GeneratedColumn<bool> isFcmDio = GeneratedColumn<bool>(
      'is_fcm_dio', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_fcm_dio" IN (0, 1))'));
  @override
  List<GeneratedColumn> get $columns => [
        uuid,
        endpoint,
        method,
        body,
        headers,
        fileName,
        fileBytes,
        fileLink,
        idAtividade,
        enviado,
        dataHora,
        isFcmDio
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'offline_request_drift';
  @override
  VerificationContext validateIntegrity(
      Insertable<OfflineRequestDriftData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('uuid')) {
      context.handle(
          _uuidMeta, uuid.isAcceptableOrUnknown(data['uuid']!, _uuidMeta));
    } else if (isInserting) {
      context.missing(_uuidMeta);
    }
    if (data.containsKey('endpoint')) {
      context.handle(_endpointMeta,
          endpoint.isAcceptableOrUnknown(data['endpoint']!, _endpointMeta));
    } else if (isInserting) {
      context.missing(_endpointMeta);
    }
    if (data.containsKey('method')) {
      context.handle(_methodMeta,
          method.isAcceptableOrUnknown(data['method']!, _methodMeta));
    } else if (isInserting) {
      context.missing(_methodMeta);
    }
    if (data.containsKey('body')) {
      context.handle(
          _bodyMeta, body.isAcceptableOrUnknown(data['body']!, _bodyMeta));
    }
    if (data.containsKey('headers')) {
      context.handle(_headersMeta,
          headers.isAcceptableOrUnknown(data['headers']!, _headersMeta));
    }
    if (data.containsKey('file_name')) {
      context.handle(_fileNameMeta,
          fileName.isAcceptableOrUnknown(data['file_name']!, _fileNameMeta));
    }
    if (data.containsKey('file_bytes')) {
      context.handle(_fileBytesMeta,
          fileBytes.isAcceptableOrUnknown(data['file_bytes']!, _fileBytesMeta));
    }
    if (data.containsKey('file_link')) {
      context.handle(_fileLinkMeta,
          fileLink.isAcceptableOrUnknown(data['file_link']!, _fileLinkMeta));
    }
    if (data.containsKey('id_atividade')) {
      context.handle(
          _idAtividadeMeta,
          idAtividade.isAcceptableOrUnknown(
              data['id_atividade']!, _idAtividadeMeta));
    } else if (isInserting) {
      context.missing(_idAtividadeMeta);
    }
    if (data.containsKey('enviado')) {
      context.handle(_enviadoMeta,
          enviado.isAcceptableOrUnknown(data['enviado']!, _enviadoMeta));
    } else if (isInserting) {
      context.missing(_enviadoMeta);
    }
    if (data.containsKey('data_hora')) {
      context.handle(_dataHoraMeta,
          dataHora.isAcceptableOrUnknown(data['data_hora']!, _dataHoraMeta));
    }
    if (data.containsKey('is_fcm_dio')) {
      context.handle(_isFcmDioMeta,
          isFcmDio.isAcceptableOrUnknown(data['is_fcm_dio']!, _isFcmDioMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => const {};
  @override
  OfflineRequestDriftData map(Map<String, dynamic> data,
      {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return OfflineRequestDriftData(
      uuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}uuid'])!,
      endpoint: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}endpoint'])!,
      method: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}method'])!,
      body: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}body']),
      headers: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}headers']),
      fileName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}file_name']),
      fileBytes: attachedDatabase.typeMapping
          .read(DriftSqlType.blob, data['${effectivePrefix}file_bytes']),
      fileLink: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}file_link']),
      idAtividade: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id_atividade'])!,
      enviado: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}enviado'])!,
      dataHora: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}data_hora']),
      isFcmDio: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_fcm_dio']),
    );
  }

  @override
  $OfflineRequestDriftTable createAlias(String alias) {
    return $OfflineRequestDriftTable(attachedDatabase, alias);
  }
}

class OfflineRequestDriftData extends DataClass
    implements Insertable<OfflineRequestDriftData> {
  final String uuid;
  final String endpoint;
  final String method;
  final String? body;
  final String? headers;
  final String? fileName;
  final Uint8List? fileBytes;
  final String? fileLink;
  final int idAtividade;
  final bool enviado;
  final DateTime? dataHora;
  final bool? isFcmDio;
  const OfflineRequestDriftData(
      {required this.uuid,
      required this.endpoint,
      required this.method,
      this.body,
      this.headers,
      this.fileName,
      this.fileBytes,
      this.fileLink,
      required this.idAtividade,
      required this.enviado,
      this.dataHora,
      this.isFcmDio});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['uuid'] = Variable<String>(uuid);
    map['endpoint'] = Variable<String>(endpoint);
    map['method'] = Variable<String>(method);
    if (!nullToAbsent || body != null) {
      map['body'] = Variable<String>(body);
    }
    if (!nullToAbsent || headers != null) {
      map['headers'] = Variable<String>(headers);
    }
    if (!nullToAbsent || fileName != null) {
      map['file_name'] = Variable<String>(fileName);
    }
    if (!nullToAbsent || fileBytes != null) {
      map['file_bytes'] = Variable<Uint8List>(fileBytes);
    }
    if (!nullToAbsent || fileLink != null) {
      map['file_link'] = Variable<String>(fileLink);
    }
    map['id_atividade'] = Variable<int>(idAtividade);
    map['enviado'] = Variable<bool>(enviado);
    if (!nullToAbsent || dataHora != null) {
      map['data_hora'] = Variable<DateTime>(dataHora);
    }
    if (!nullToAbsent || isFcmDio != null) {
      map['is_fcm_dio'] = Variable<bool>(isFcmDio);
    }
    return map;
  }

  OfflineRequestDriftCompanion toCompanion(bool nullToAbsent) {
    return OfflineRequestDriftCompanion(
      uuid: Value(uuid),
      endpoint: Value(endpoint),
      method: Value(method),
      body: body == null && nullToAbsent ? const Value.absent() : Value(body),
      headers: headers == null && nullToAbsent
          ? const Value.absent()
          : Value(headers),
      fileName: fileName == null && nullToAbsent
          ? const Value.absent()
          : Value(fileName),
      fileBytes: fileBytes == null && nullToAbsent
          ? const Value.absent()
          : Value(fileBytes),
      fileLink: fileLink == null && nullToAbsent
          ? const Value.absent()
          : Value(fileLink),
      idAtividade: Value(idAtividade),
      enviado: Value(enviado),
      dataHora: dataHora == null && nullToAbsent
          ? const Value.absent()
          : Value(dataHora),
      isFcmDio: isFcmDio == null && nullToAbsent
          ? const Value.absent()
          : Value(isFcmDio),
    );
  }

  factory OfflineRequestDriftData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return OfflineRequestDriftData(
      uuid: serializer.fromJson<String>(json['uuid']),
      endpoint: serializer.fromJson<String>(json['endpoint']),
      method: serializer.fromJson<String>(json['method']),
      body: serializer.fromJson<String?>(json['body']),
      headers: serializer.fromJson<String?>(json['headers']),
      fileName: serializer.fromJson<String?>(json['fileName']),
      fileBytes: serializer.fromJson<Uint8List?>(json['fileBytes']),
      fileLink: serializer.fromJson<String?>(json['fileLink']),
      idAtividade: serializer.fromJson<int>(json['idAtividade']),
      enviado: serializer.fromJson<bool>(json['enviado']),
      dataHora: serializer.fromJson<DateTime?>(json['dataHora']),
      isFcmDio: serializer.fromJson<bool?>(json['isFcmDio']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'uuid': serializer.toJson<String>(uuid),
      'endpoint': serializer.toJson<String>(endpoint),
      'method': serializer.toJson<String>(method),
      'body': serializer.toJson<String?>(body),
      'headers': serializer.toJson<String?>(headers),
      'fileName': serializer.toJson<String?>(fileName),
      'fileBytes': serializer.toJson<Uint8List?>(fileBytes),
      'fileLink': serializer.toJson<String?>(fileLink),
      'idAtividade': serializer.toJson<int>(idAtividade),
      'enviado': serializer.toJson<bool>(enviado),
      'dataHora': serializer.toJson<DateTime?>(dataHora),
      'isFcmDio': serializer.toJson<bool?>(isFcmDio),
    };
  }

  OfflineRequestDriftData copyWith(
          {String? uuid,
          String? endpoint,
          String? method,
          Value<String?> body = const Value.absent(),
          Value<String?> headers = const Value.absent(),
          Value<String?> fileName = const Value.absent(),
          Value<Uint8List?> fileBytes = const Value.absent(),
          Value<String?> fileLink = const Value.absent(),
          int? idAtividade,
          bool? enviado,
          Value<DateTime?> dataHora = const Value.absent(),
          Value<bool?> isFcmDio = const Value.absent()}) =>
      OfflineRequestDriftData(
        uuid: uuid ?? this.uuid,
        endpoint: endpoint ?? this.endpoint,
        method: method ?? this.method,
        body: body.present ? body.value : this.body,
        headers: headers.present ? headers.value : this.headers,
        fileName: fileName.present ? fileName.value : this.fileName,
        fileBytes: fileBytes.present ? fileBytes.value : this.fileBytes,
        fileLink: fileLink.present ? fileLink.value : this.fileLink,
        idAtividade: idAtividade ?? this.idAtividade,
        enviado: enviado ?? this.enviado,
        dataHora: dataHora.present ? dataHora.value : this.dataHora,
        isFcmDio: isFcmDio.present ? isFcmDio.value : this.isFcmDio,
      );
  OfflineRequestDriftData copyWithCompanion(OfflineRequestDriftCompanion data) {
    return OfflineRequestDriftData(
      uuid: data.uuid.present ? data.uuid.value : this.uuid,
      endpoint: data.endpoint.present ? data.endpoint.value : this.endpoint,
      method: data.method.present ? data.method.value : this.method,
      body: data.body.present ? data.body.value : this.body,
      headers: data.headers.present ? data.headers.value : this.headers,
      fileName: data.fileName.present ? data.fileName.value : this.fileName,
      fileBytes: data.fileBytes.present ? data.fileBytes.value : this.fileBytes,
      fileLink: data.fileLink.present ? data.fileLink.value : this.fileLink,
      idAtividade:
          data.idAtividade.present ? data.idAtividade.value : this.idAtividade,
      enviado: data.enviado.present ? data.enviado.value : this.enviado,
      dataHora: data.dataHora.present ? data.dataHora.value : this.dataHora,
      isFcmDio: data.isFcmDio.present ? data.isFcmDio.value : this.isFcmDio,
    );
  }

  @override
  String toString() {
    return (StringBuffer('OfflineRequestDriftData(')
          ..write('uuid: $uuid, ')
          ..write('endpoint: $endpoint, ')
          ..write('method: $method, ')
          ..write('body: $body, ')
          ..write('headers: $headers, ')
          ..write('fileName: $fileName, ')
          ..write('fileBytes: $fileBytes, ')
          ..write('fileLink: $fileLink, ')
          ..write('idAtividade: $idAtividade, ')
          ..write('enviado: $enviado, ')
          ..write('dataHora: $dataHora, ')
          ..write('isFcmDio: $isFcmDio')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      uuid,
      endpoint,
      method,
      body,
      headers,
      fileName,
      $driftBlobEquality.hash(fileBytes),
      fileLink,
      idAtividade,
      enviado,
      dataHora,
      isFcmDio);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is OfflineRequestDriftData &&
          other.uuid == this.uuid &&
          other.endpoint == this.endpoint &&
          other.method == this.method &&
          other.body == this.body &&
          other.headers == this.headers &&
          other.fileName == this.fileName &&
          $driftBlobEquality.equals(other.fileBytes, this.fileBytes) &&
          other.fileLink == this.fileLink &&
          other.idAtividade == this.idAtividade &&
          other.enviado == this.enviado &&
          other.dataHora == this.dataHora &&
          other.isFcmDio == this.isFcmDio);
}

class OfflineRequestDriftCompanion
    extends UpdateCompanion<OfflineRequestDriftData> {
  final Value<String> uuid;
  final Value<String> endpoint;
  final Value<String> method;
  final Value<String?> body;
  final Value<String?> headers;
  final Value<String?> fileName;
  final Value<Uint8List?> fileBytes;
  final Value<String?> fileLink;
  final Value<int> idAtividade;
  final Value<bool> enviado;
  final Value<DateTime?> dataHora;
  final Value<bool?> isFcmDio;
  final Value<int> rowid;
  const OfflineRequestDriftCompanion({
    this.uuid = const Value.absent(),
    this.endpoint = const Value.absent(),
    this.method = const Value.absent(),
    this.body = const Value.absent(),
    this.headers = const Value.absent(),
    this.fileName = const Value.absent(),
    this.fileBytes = const Value.absent(),
    this.fileLink = const Value.absent(),
    this.idAtividade = const Value.absent(),
    this.enviado = const Value.absent(),
    this.dataHora = const Value.absent(),
    this.isFcmDio = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  OfflineRequestDriftCompanion.insert({
    required String uuid,
    required String endpoint,
    required String method,
    this.body = const Value.absent(),
    this.headers = const Value.absent(),
    this.fileName = const Value.absent(),
    this.fileBytes = const Value.absent(),
    this.fileLink = const Value.absent(),
    required int idAtividade,
    required bool enviado,
    this.dataHora = const Value.absent(),
    this.isFcmDio = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : uuid = Value(uuid),
        endpoint = Value(endpoint),
        method = Value(method),
        idAtividade = Value(idAtividade),
        enviado = Value(enviado);
  static Insertable<OfflineRequestDriftData> custom({
    Expression<String>? uuid,
    Expression<String>? endpoint,
    Expression<String>? method,
    Expression<String>? body,
    Expression<String>? headers,
    Expression<String>? fileName,
    Expression<Uint8List>? fileBytes,
    Expression<String>? fileLink,
    Expression<int>? idAtividade,
    Expression<bool>? enviado,
    Expression<DateTime>? dataHora,
    Expression<bool>? isFcmDio,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (uuid != null) 'uuid': uuid,
      if (endpoint != null) 'endpoint': endpoint,
      if (method != null) 'method': method,
      if (body != null) 'body': body,
      if (headers != null) 'headers': headers,
      if (fileName != null) 'file_name': fileName,
      if (fileBytes != null) 'file_bytes': fileBytes,
      if (fileLink != null) 'file_link': fileLink,
      if (idAtividade != null) 'id_atividade': idAtividade,
      if (enviado != null) 'enviado': enviado,
      if (dataHora != null) 'data_hora': dataHora,
      if (isFcmDio != null) 'is_fcm_dio': isFcmDio,
      if (rowid != null) 'rowid': rowid,
    });
  }

  OfflineRequestDriftCompanion copyWith(
      {Value<String>? uuid,
      Value<String>? endpoint,
      Value<String>? method,
      Value<String?>? body,
      Value<String?>? headers,
      Value<String?>? fileName,
      Value<Uint8List?>? fileBytes,
      Value<String?>? fileLink,
      Value<int>? idAtividade,
      Value<bool>? enviado,
      Value<DateTime?>? dataHora,
      Value<bool?>? isFcmDio,
      Value<int>? rowid}) {
    return OfflineRequestDriftCompanion(
      uuid: uuid ?? this.uuid,
      endpoint: endpoint ?? this.endpoint,
      method: method ?? this.method,
      body: body ?? this.body,
      headers: headers ?? this.headers,
      fileName: fileName ?? this.fileName,
      fileBytes: fileBytes ?? this.fileBytes,
      fileLink: fileLink ?? this.fileLink,
      idAtividade: idAtividade ?? this.idAtividade,
      enviado: enviado ?? this.enviado,
      dataHora: dataHora ?? this.dataHora,
      isFcmDio: isFcmDio ?? this.isFcmDio,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (uuid.present) {
      map['uuid'] = Variable<String>(uuid.value);
    }
    if (endpoint.present) {
      map['endpoint'] = Variable<String>(endpoint.value);
    }
    if (method.present) {
      map['method'] = Variable<String>(method.value);
    }
    if (body.present) {
      map['body'] = Variable<String>(body.value);
    }
    if (headers.present) {
      map['headers'] = Variable<String>(headers.value);
    }
    if (fileName.present) {
      map['file_name'] = Variable<String>(fileName.value);
    }
    if (fileBytes.present) {
      map['file_bytes'] = Variable<Uint8List>(fileBytes.value);
    }
    if (fileLink.present) {
      map['file_link'] = Variable<String>(fileLink.value);
    }
    if (idAtividade.present) {
      map['id_atividade'] = Variable<int>(idAtividade.value);
    }
    if (enviado.present) {
      map['enviado'] = Variable<bool>(enviado.value);
    }
    if (dataHora.present) {
      map['data_hora'] = Variable<DateTime>(dataHora.value);
    }
    if (isFcmDio.present) {
      map['is_fcm_dio'] = Variable<bool>(isFcmDio.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('OfflineRequestDriftCompanion(')
          ..write('uuid: $uuid, ')
          ..write('endpoint: $endpoint, ')
          ..write('method: $method, ')
          ..write('body: $body, ')
          ..write('headers: $headers, ')
          ..write('fileName: $fileName, ')
          ..write('fileBytes: $fileBytes, ')
          ..write('fileLink: $fileLink, ')
          ..write('idAtividade: $idAtividade, ')
          ..write('enviado: $enviado, ')
          ..write('dataHora: $dataHora, ')
          ..write('isFcmDio: $isFcmDio, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

abstract class _$OfflineRequestDatabaseDrift extends GeneratedDatabase {
  _$OfflineRequestDatabaseDrift(QueryExecutor e) : super(e);
  $OfflineRequestDatabaseDriftManager get managers =>
      $OfflineRequestDatabaseDriftManager(this);
  late final $OfflineRequestDriftTable offlineRequestDrift =
      $OfflineRequestDriftTable(this);
  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities => [offlineRequestDrift];
}

typedef $$OfflineRequestDriftTableCreateCompanionBuilder
    = OfflineRequestDriftCompanion Function({
  required String uuid,
  required String endpoint,
  required String method,
  Value<String?> body,
  Value<String?> headers,
  Value<String?> fileName,
  Value<Uint8List?> fileBytes,
  Value<String?> fileLink,
  required int idAtividade,
  required bool enviado,
  Value<DateTime?> dataHora,
  Value<bool?> isFcmDio,
  Value<int> rowid,
});
typedef $$OfflineRequestDriftTableUpdateCompanionBuilder
    = OfflineRequestDriftCompanion Function({
  Value<String> uuid,
  Value<String> endpoint,
  Value<String> method,
  Value<String?> body,
  Value<String?> headers,
  Value<String?> fileName,
  Value<Uint8List?> fileBytes,
  Value<String?> fileLink,
  Value<int> idAtividade,
  Value<bool> enviado,
  Value<DateTime?> dataHora,
  Value<bool?> isFcmDio,
  Value<int> rowid,
});

class $$OfflineRequestDriftTableFilterComposer
    extends Composer<_$OfflineRequestDatabaseDrift, $OfflineRequestDriftTable> {
  $$OfflineRequestDriftTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get uuid => $composableBuilder(
      column: $table.uuid, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get endpoint => $composableBuilder(
      column: $table.endpoint, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get method => $composableBuilder(
      column: $table.method, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get body => $composableBuilder(
      column: $table.body, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get headers => $composableBuilder(
      column: $table.headers, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get fileName => $composableBuilder(
      column: $table.fileName, builder: (column) => ColumnFilters(column));

  ColumnFilters<Uint8List> get fileBytes => $composableBuilder(
      column: $table.fileBytes, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get fileLink => $composableBuilder(
      column: $table.fileLink, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get idAtividade => $composableBuilder(
      column: $table.idAtividade, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get enviado => $composableBuilder(
      column: $table.enviado, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get dataHora => $composableBuilder(
      column: $table.dataHora, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isFcmDio => $composableBuilder(
      column: $table.isFcmDio, builder: (column) => ColumnFilters(column));
}

class $$OfflineRequestDriftTableOrderingComposer
    extends Composer<_$OfflineRequestDatabaseDrift, $OfflineRequestDriftTable> {
  $$OfflineRequestDriftTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get uuid => $composableBuilder(
      column: $table.uuid, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get endpoint => $composableBuilder(
      column: $table.endpoint, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get method => $composableBuilder(
      column: $table.method, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get body => $composableBuilder(
      column: $table.body, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get headers => $composableBuilder(
      column: $table.headers, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get fileName => $composableBuilder(
      column: $table.fileName, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<Uint8List> get fileBytes => $composableBuilder(
      column: $table.fileBytes, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get fileLink => $composableBuilder(
      column: $table.fileLink, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get idAtividade => $composableBuilder(
      column: $table.idAtividade, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get enviado => $composableBuilder(
      column: $table.enviado, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get dataHora => $composableBuilder(
      column: $table.dataHora, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isFcmDio => $composableBuilder(
      column: $table.isFcmDio, builder: (column) => ColumnOrderings(column));
}

class $$OfflineRequestDriftTableAnnotationComposer
    extends Composer<_$OfflineRequestDatabaseDrift, $OfflineRequestDriftTable> {
  $$OfflineRequestDriftTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get uuid =>
      $composableBuilder(column: $table.uuid, builder: (column) => column);

  GeneratedColumn<String> get endpoint =>
      $composableBuilder(column: $table.endpoint, builder: (column) => column);

  GeneratedColumn<String> get method =>
      $composableBuilder(column: $table.method, builder: (column) => column);

  GeneratedColumn<String> get body =>
      $composableBuilder(column: $table.body, builder: (column) => column);

  GeneratedColumn<String> get headers =>
      $composableBuilder(column: $table.headers, builder: (column) => column);

  GeneratedColumn<String> get fileName =>
      $composableBuilder(column: $table.fileName, builder: (column) => column);

  GeneratedColumn<Uint8List> get fileBytes =>
      $composableBuilder(column: $table.fileBytes, builder: (column) => column);

  GeneratedColumn<String> get fileLink =>
      $composableBuilder(column: $table.fileLink, builder: (column) => column);

  GeneratedColumn<int> get idAtividade => $composableBuilder(
      column: $table.idAtividade, builder: (column) => column);

  GeneratedColumn<bool> get enviado =>
      $composableBuilder(column: $table.enviado, builder: (column) => column);

  GeneratedColumn<DateTime> get dataHora =>
      $composableBuilder(column: $table.dataHora, builder: (column) => column);

  GeneratedColumn<bool> get isFcmDio =>
      $composableBuilder(column: $table.isFcmDio, builder: (column) => column);
}

class $$OfflineRequestDriftTableTableManager extends RootTableManager<
    _$OfflineRequestDatabaseDrift,
    $OfflineRequestDriftTable,
    OfflineRequestDriftData,
    $$OfflineRequestDriftTableFilterComposer,
    $$OfflineRequestDriftTableOrderingComposer,
    $$OfflineRequestDriftTableAnnotationComposer,
    $$OfflineRequestDriftTableCreateCompanionBuilder,
    $$OfflineRequestDriftTableUpdateCompanionBuilder,
    (
      OfflineRequestDriftData,
      BaseReferences<_$OfflineRequestDatabaseDrift, $OfflineRequestDriftTable,
          OfflineRequestDriftData>
    ),
    OfflineRequestDriftData,
    PrefetchHooks Function()> {
  $$OfflineRequestDriftTableTableManager(
      _$OfflineRequestDatabaseDrift db, $OfflineRequestDriftTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$OfflineRequestDriftTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$OfflineRequestDriftTableOrderingComposer(
                  $db: db, $table: table),
          createComputedFieldComposer: () =>
              $$OfflineRequestDriftTableAnnotationComposer(
                  $db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> uuid = const Value.absent(),
            Value<String> endpoint = const Value.absent(),
            Value<String> method = const Value.absent(),
            Value<String?> body = const Value.absent(),
            Value<String?> headers = const Value.absent(),
            Value<String?> fileName = const Value.absent(),
            Value<Uint8List?> fileBytes = const Value.absent(),
            Value<String?> fileLink = const Value.absent(),
            Value<int> idAtividade = const Value.absent(),
            Value<bool> enviado = const Value.absent(),
            Value<DateTime?> dataHora = const Value.absent(),
            Value<bool?> isFcmDio = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              OfflineRequestDriftCompanion(
            uuid: uuid,
            endpoint: endpoint,
            method: method,
            body: body,
            headers: headers,
            fileName: fileName,
            fileBytes: fileBytes,
            fileLink: fileLink,
            idAtividade: idAtividade,
            enviado: enviado,
            dataHora: dataHora,
            isFcmDio: isFcmDio,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String uuid,
            required String endpoint,
            required String method,
            Value<String?> body = const Value.absent(),
            Value<String?> headers = const Value.absent(),
            Value<String?> fileName = const Value.absent(),
            Value<Uint8List?> fileBytes = const Value.absent(),
            Value<String?> fileLink = const Value.absent(),
            required int idAtividade,
            required bool enviado,
            Value<DateTime?> dataHora = const Value.absent(),
            Value<bool?> isFcmDio = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              OfflineRequestDriftCompanion.insert(
            uuid: uuid,
            endpoint: endpoint,
            method: method,
            body: body,
            headers: headers,
            fileName: fileName,
            fileBytes: fileBytes,
            fileLink: fileLink,
            idAtividade: idAtividade,
            enviado: enviado,
            dataHora: dataHora,
            isFcmDio: isFcmDio,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$OfflineRequestDriftTableProcessedTableManager = ProcessedTableManager<
    _$OfflineRequestDatabaseDrift,
    $OfflineRequestDriftTable,
    OfflineRequestDriftData,
    $$OfflineRequestDriftTableFilterComposer,
    $$OfflineRequestDriftTableOrderingComposer,
    $$OfflineRequestDriftTableAnnotationComposer,
    $$OfflineRequestDriftTableCreateCompanionBuilder,
    $$OfflineRequestDriftTableUpdateCompanionBuilder,
    (
      OfflineRequestDriftData,
      BaseReferences<_$OfflineRequestDatabaseDrift, $OfflineRequestDriftTable,
          OfflineRequestDriftData>
    ),
    OfflineRequestDriftData,
    PrefetchHooks Function()>;

class $OfflineRequestDatabaseDriftManager {
  final _$OfflineRequestDatabaseDrift _db;
  $OfflineRequestDatabaseDriftManager(this._db);
  $$OfflineRequestDriftTableTableManager get offlineRequestDrift =>
      $$OfflineRequestDriftTableTableManager(_db, _db.offlineRequestDrift);
}
