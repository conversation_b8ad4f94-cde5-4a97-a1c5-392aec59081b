import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/components/buttom_ls/button_ls_custom.dart';
import 'package:octalog/src/components/custom_scaffold/custom_scaffold.dart';
import 'package:octalog/src/components/loading_ls/loading_ls.dart';
//import 'package:octalog/src/helpers/api_ls.dart';
import 'package:octalog/src/helpers/login/login.dart';
import 'package:octalog/src/helpers/web_connector.dart';
//import 'package:octalog/src/helpers/web_socket/hub_helper.dart';
import 'package:octalog/src/models/id_status_atividade_enum.dart';
import 'package:octalog/src/pages/home/<USER>/custom_listview_home.dart';
import 'package:octalog/src/pages/home/<USER>/transferencia/transferencia.dart';
import 'package:octalog/src/pages/home/<USER>';
import 'package:octalog/src/pages/sac_page/sac_page.dart';
import 'package:octalog/src/pages/home/<USER>/text_eventos_restantes.dart';

import '../../database/config_blob/config_database.dart';
import '../../database/sac/sac_atendimento.dart';
import '../../models_new/endereco_new.dart';
import '../../utils/colors.dart';
import '../../utils/offline_helper.dart';
import '../../utils/polylines.dart';
import '../../utils/verify_datetime.dart';
import '../coleta_home/coleta_home.dart';
import '../entrega_newpages/controller/entrega_new_store.dart';
import '../entrega_newpages/entrega_new_page.dart';
import '../mapa_page/mapa_page.dart';
import '../meus_enderecos/meus_enderecos.dart';
import '../romaneio/romaneio_page.dart';
import '../search/search.dart';
//import 'components/atividade_cronometro_tile.dart';
import 'components/aviso_scronimos_pendente.dart';
import 'components/custom_drawer.dart';
import 'components/custom_enum_navigation.dart';
import 'components/custom_fcm_view_home.dart';
import 'home_controller.dart';

class Home extends StatefulWidget {
  final bool enterAtividade;
  final IdStatusAtividadeEnum? idStatusAtividade;
  final Position? position;
  const Home({super.key, this.enterAtividade = true, this.idStatusAtividade, this.position});

  @override
  State<Home> createState() => _HomeState();
}

class _HomeState extends State<Home> {
  final controller = HomeController.instance;
  bool iscoletaL = false;
  HomeState get stateValue => controller.state.value;

  EnderecoNew? atividadeOffline;

  Future<void> init() async {
    if (Login.instance.usuarioLogado?.usuario == "3329") return;

    try {
      await verifyDateTime(context);

      final diferents = DateTime.now().difference(stateValue.dataForceUltimaSincronizacao ?? DateTime.now()).inMinutes.abs();

      if (diferents >= 5 || stateValue.dataForceUltimaSincronizacao == null) {
        final responseConfig = await ConfigDatabase.instance.getConfig();

        bool continuar = await entrarTelaSicronismo(minimo: responseConfig.qtdeForceSincronismoItens);
        if (!continuar) return;
      }

      PedidosSacModel? sacEmAberto = await verificarSacEmAberto();

      if (sacEmAberto != null) {
        await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (_) => SacPage(idStatusAtividade: sacEmAberto.idStatusAtividade, idos: sacEmAberto.idos, idSacAtendimento: sacEmAberto.idSacAtendimento),
          ),
        );
      }

      await EntregaNewStore.getStateDb().then((state) async {
        if (state == null) return;
        final atividadeModel = state.atividade;
        setState(() => atividadeOffline = atividadeModel);
        if (!widget.enterAtividade) return;
        Navigator.push(
          context,
          MaterialPageRoute(builder: (_) => EntregaNewPage(atividade: atividadeModel, atividadeAnterior: atividadeOffline, state: state)),
        );
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<PedidosSacModel?> verificarSacEmAberto() async {
    final pedidosSacDatabase = PedidosSacDatabase.instance;
    final pedidosSac = await pedidosSacDatabase.getAllSac();
    if (pedidosSac.isEmpty) return null;

    final conn = WebConnector();
    try {
      final response = await conn.get('/sac/buscar-sac-em-aberto');
      if (response.statusCode == 200) {
        final json = response.data;
        final sacEmAberto = PedidosSacModel.fromJson(json);
        log('sacEmAberto: $sacEmAberto');

        return sacEmAberto;
      }
    } catch (_) {
      await Future.delayed(const Duration(seconds: 10));
    }
    return null;
  }

  Future<void> _initWithBuscar() async {
    final responseConfig = await ConfigDatabase.instance.getConfig();
    // if (responseConfig.ativarLigacao) {
    //   HubHelper.getInstance(responseConfig.linkHub);
    // }
    await controller.fetchAtividades(inicioHome: responseConfig.buscarHome);
    init();
  }

  @override
  void initState() {
    super.initState();
    _initWithBuscar();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: controller.state,
      builder: (context, state, _) {
        bool pesquisa = state.search.isNotEmpty;

        final loading = state.loading;
        final data = state.atividadesFiltradas;
        final pageSelected = state.pageSelected;
        final isInicio = pageSelected.index == HomeNavigationEnum.home.index;
        final isSearch = pageSelected.index == HomeNavigationEnum.search.index;
        final isRota = pageSelected.index == HomeNavigationEnum.route.index;
        final usuarioLogado = Login.instance.usuarioLogado;
        final isTransferencia = pageSelected.index == HomeNavigationEnum.transferencia.index;
        final isColetar = pageSelected.index == HomeNavigationEnum.coletar.index;
        final isRoteirizar = pageSelected.index == HomeNavigationEnum.routing.index;

        var usuarioTransferenciaEnable = usuarioLogado?.permiteTransferenciaMobile ?? false;
        var listaHomeButtons = HomeNavigationEnum.values;

        if (!usuarioTransferenciaEnable) {
          listaHomeButtons = listaHomeButtons.where((e) => e != HomeNavigationEnum.transferencia).toList();
        }

        final isExpedicao = state.isExpedicao;
        return KeyboardVisibilityBuilder(
          builder: (context, isKeyboardVisible) {
            return CustomScaffold(
              canPop: false,
              isExpedicao: isExpedicao,
              bottomNavigationBar:
                  isKeyboardVisible
                      ? null
                      : Stack(
                        children: [
                          // if (!state.listColetaAdd &&
                          //     usuarioColetaEnable &&
                          //     !usuarioTransferenciaEnable)
                          //   Positioned(
                          //     child: Row(
                          //       mainAxisAlignment: MainAxisAlignment.center,
                          //       children: [
                          //         ClipRRect(
                          //           borderRadius: BorderRadius.circular(40),
                          //           child: Container(
                          //             width: 70,
                          //             height: 70,
                          //             color: ColorsCustom.customOrange,
                          //           ),
                          //         ),
                          //       ],
                          //     ),
                          //   ),
                          Padding(
                            padding: const EdgeInsets.only(bottom: 0, left: 0, right: 0),
                            child: SizedBox(
                              height: 70,
                              child: Card(
                                margin: const EdgeInsets.only(top: 10),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(0),
                                  child:
                                      state.listColetaAdd && state.pageSelected == HomeNavigationEnum.coletar ||
                                              state.pageSelected == HomeNavigationEnum.transferencia
                                          ? ButtonLsCustom(
                                            isLoading: (state.pageSelected == HomeNavigationEnum.transferencia && state.loadingButtonTransferencia),
                                            text: state.pageSelected == HomeNavigationEnum.transferencia ? 'CONFIRMAR' : 'LISTA DE PEDIDOS',
                                            onPressed: () {
                                              if (state.pageSelected == HomeNavigationEnum.transferencia) {
                                                controller.confirmarTransferencia();
                                                return;
                                              }
                                              setState(() {
                                                iscoletaL = false;
                                              });
                                              controller.fetchAtividades();
                                              controller.setPageSelected(HomeNavigationEnum.home);
                                            },
                                          )
                                          : BottomNavigationBar(
                                            showSelectedLabels: true,
                                            showUnselectedLabels: false,
                                            type: BottomNavigationBarType.fixed,
                                            iconSize: 25,
                                            elevation: 0,
                                            backgroundColor: ColorsCustom.customOrange,
                                            currentIndex: listaHomeButtons.indexOf(pageSelected),
                                            onTap: (value) {
                                              setState(() {
                                                iscoletaL = false;
                                              });

                                              final HomeNavigationEnum nav = listaHomeButtons[value];

                                              if (offlineStore.isOffline && !nav.workOffline) {
                                                showDialog(
                                                  context: context,
                                                  builder: (ctx) {
                                                    return AlertDialog(
                                                      title: const Text('Sem conexão'),
                                                      content: const Text('O aplicativo está offline, para seguir necessário ter internet.'),
                                                      actions: <Widget>[
                                                        TextButton(
                                                          child: const Text('OK', style: TextStyle(color: Colors.orange)),
                                                          onPressed: () {
                                                            Navigator.pop(ctx);
                                                          },
                                                        ),
                                                      ],
                                                    );
                                                  },
                                                );
                                                return;
                                              }

                                              if (value == HomeNavigationEnum.coletar.index) {
                                                setState(() => iscoletaL = true);
                                              }
                                              controller.setPageSelected(nav);
                                            },
                                            selectedItemColor: Colors.white,
                                            unselectedItemColor: Colors.white.withOpacity(0.7),
                                            unselectedLabelStyle: GoogleFonts.roboto(fontSize: 12, fontWeight: FontWeight.w500),
                                            items:
                                                listaHomeButtons.map((e) {
                                                  if (e.title == 'Coletar' && !usuarioTransferenciaEnable) {
                                                    return const BottomNavigationBarItem(icon: Icon(Icons.add, size: 0), label: '');
                                                  }

                                                  return BottomNavigationBarItem(icon: e.icon, label: e.title);
                                                }).toList(),
                                          ),
                                ),
                              ),
                            ),
                          ),
                          // if (!state.listColetaAdd &&
                          //     usuarioColetaEnable &&
                          //     !usuarioTransferenciaEnable)
                          //   Positioned(
                          //     child: Row(
                          //       mainAxisAlignment: MainAxisAlignment.center,
                          //       children: [
                          //         GestureDetector(
                          //           onTap: () {
                          //             setState(() {
                          //               iscoletaL = true;
                          //             });
                          //             controller.setPageSelected(
                          //                 HomeNavigationEnum.coletar);
                          //           },
                          //           child: ClipRRect(
                          //             borderRadius: BorderRadius.circular(40),
                          //             child: SizedBox(
                          //               width: 70,
                          //               height: 70,
                          //               child: Column(
                          //                 mainAxisAlignment:
                          //                     MainAxisAlignment.center,
                          //                 children: [
                          //                   Image.asset(
                          //                     'assets/images/menu_circular.png',
                          //                     width: 30,
                          //                     height: 30,
                          //                     color: iscoletaL
                          //                         ? Colors.white
                          //                         : Colors.white.withOpacity(0.7),
                          //                   ),
                          //                   iscoletaL
                          //                       ? const Text(
                          //                           'Coleta',
                          //                           style: TextStyle(
                          //                             color: Colors.white,
                          //                             fontSize: 15,
                          //                           ),
                          //                         )
                          //                       : const SizedBox(
                          //                           height: 0,
                          //                         ),
                          //                 ],
                          //               ),
                          //             ),
                          //           ),
                          //         ),
                          //       ],
                          //     ),
                          //   )
                        ],
                      ),
              backgroundColor: Colors.white,
              floatingActionButton: isInicio ? Container() : null,
              showFloatingActionButtonOnOffLine: false,
              child:
                  isTransferencia
                      ? TransferenciaWidget(controller: controller)
                      : isColetar
                      ? ColetaHome(controller: controller)
                      : isRoteirizar
                      ? MeusEnderecos(controller: controller)
                      : isRota
                      ? FutureBuilder<PolyLinesMap>(
                        future: getPolylines(data),
                        builder: (context, s) {
                          if (!s.hasData && !s.hasError) {
                            return const LoadingLs();
                          }
                          final resp = s.data;
                          return MapaPage(entregas: data, reentregas: const [], polyLinesMap: resp ?? PolyLinesMap(distance: 0, polyLines: []));
                        },
                      )
                      : isSearch
                      ? Search(controller: controller)
                      : Column(
                        children: [
                          // if (atividadeOffline != null)
                          //   AtividadeCronometroTile(
                          //     atividade: atividadeOffline!,
                          //   ),
                          Container(
                            color: ColorsCustom.customOrange, // Cor de fundo
                            height: 10, // Altura do SizedBox
                          ),
                          Container(
                            color: ColorsCustom.customOrange, // Cor de fundo que você deseja
                            child: Row(
                              children: [
                                const SizedBox(width: 10),
                                GestureDetector(
                                  onTap: () {
                                    Navigator.of(context).push(MaterialPageRoute(builder: (context) => const CustomDrawer()));
                                  },
                                  child: Padding(
                                    padding: EdgeInsets.all(8.0), // Add padding around the SizedBox
                                    child: SizedBox(width: 60, height: 60, child: Login.instance.fotoUsuarioLogado(25, true)),
                                  ),
                                ),
                                const SizedBox(width: 10),

                                // texto informando quantas entregas tem
                                Expanded(
                                  child: Builder(
                                    builder: (context) {
                                      final statusEscolhido = state.statusAtividade;

                                      final data2 =
                                          data.map((e) {
                                            return e.whereStatus(statusEscolhido);
                                          }).toList();
                                      final length = data2.fold<int>(0, (p, e) => p + e.volumesLength);
                                      String text = '';
                                      if (length == 0) {
                                        text = 'Nenhuma entrega';
                                      } else if (length == 1) {
                                        text = '1 volume ${statusEscolhido.headTitle}';
                                      } else {
                                        text = '$length volumes ${statusEscolhido.headTitlePlu}';
                                      }
                                      return GestureDetector(
                                        onTap: () {
                                          Navigator.of(context).push(MaterialPageRoute(builder: (context) => const RomaneioPage()));
                                        },
                                        child: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                Expanded(
                                                  child: Text(
                                                    text,
                                                    // textAlign:
                                                    //     TextAlign
                                                    //         .center,
                                                    style: GoogleFonts.roboto(fontSize: 15, color: Colors.white, fontWeight: FontWeight.w500),
                                                    overflow: TextOverflow.ellipsis,
                                                    maxLines: 1,
                                                  ),
                                                ),
                                                const Icon(Icons.arrow_forward_ios, size: 18, color: ColorsCustom.customOrange),
                                              ],
                                            ),
                                            const TextEventosRestantes(),
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                                ),

                                Builder(
                                  builder: (context) {
                                    return GestureDetector(
                                      onTap:
                                          //()
                                          //  async {
                                          //   showDialog(
                                          //       context: context,
                                          //       builder: (context) =>
                                          //           const FcmAlertDialogWidget());
                                          // },
                                          state.loading ? null : controller.fetchAtividades,
                                      child: Icon(Icons.refresh, size: 36, color: Colors.white),
                                    );
                                  },
                                ),
                                const SizedBox(width: 5),
                              ],
                            ),
                          ),
                          Container(
                            color: ColorsCustom.customOrange, // Cor de fundo que você deseja
                            child: SizedBox(
                              height: 42,
                              width: double.infinity,
                              child: Builder(
                                builder: (_) {
                                  final statusEscolhido = state.statusAtividade;
                                  final counti = state.countStatusAtividade;
                                  return ListView(
                                    scrollDirection: Axis.horizontal,
                                    children:
                                        [
                                          {'label': 'ENTREGAS', 'status': IdStatusAtividadeEnum.entrega},
                                          {'label': 'NEGATIVAS', 'status': IdStatusAtividadeEnum.negativa},
                                          {'label': 'ENTREGUE', 'status': IdStatusAtividadeEnum.entregue},
                                          {'label': 'CANCELADAS', 'status': IdStatusAtividadeEnum.cancelada},
                                        ].map((e) {
                                          final idStatusAtividade = e['status'] as IdStatusAtividadeEnum;
                                          final selecionado = idStatusAtividade == statusEscolhido;
                                          final String label = e['label'] as String;
                                          final count = counti[idStatusAtividade.index] ?? 0;
                                          return Padding(
                                            padding: const EdgeInsets.symmetric(horizontal: 0),
                                            child: GestureDetector(
                                              onTap: () {
                                                controller.setIdStatusAtividade(idStatusAtividade);
                                              },
                                              child: Card(
                                                elevation: 0,
                                                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(2.0)),
                                                color: ColorsCustom.customOrange.withOpacity(selecionado ? 1 : 0.3),
                                                child: SizedBox(
                                                  width: 95,
                                                  height: 36,
                                                  child: Center(
                                                    child: Text(
                                                      count > 0 ? '$label ($count)' : label,
                                                      style: GoogleFonts.roboto(
                                                        fontSize: 12,
                                                        fontWeight: selecionado ? FontWeight.bold : FontWeight.normal,
                                                        color: selecionado ? Colors.yellow : Colors.white,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          );
                                        }).toList(),
                                  );
                                },
                              ),
                            ),
                          ),
                          const SizedBox(height: 5),
                          Expanded(
                            child: RefreshIndicator(
                              onRefresh: () async {
                                await controller.fetchAtividades();
                              },
                              child:
                                  loading
                                      ? const LoadingLs()
                                      : data.isEmpty && state.deslocamentosColeta.isEmpty
                                      ? Center(
                                        child: Column(
                                          children: [
                                            Container(
                                              margin: const EdgeInsets.symmetric(horizontal: 20),
                                              child: Row(
                                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                children: [
                                                  if (pesquisa)
                                                    Expanded(
                                                      child: Text(
                                                        'Resultado da pesquisa: ${state.search}',
                                                        style: GoogleFonts.roboto(fontSize: 16, fontWeight: FontWeight.w500, color: ColorsCustom.customGrey),
                                                        maxLines: 2,
                                                        overflow: TextOverflow.ellipsis,
                                                      ),
                                                    ),
                                                  TextButton(
                                                    onPressed: () {
                                                      controller.setSearch('');
                                                    },
                                                    child:
                                                        pesquisa ? Text('Limpar pesquisa', style: GoogleFonts.roboto(color: Colors.blueGrey)) : const Text(''),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            const SizedBox(height: 150),
                                            Column(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Text(
                                                  pesquisa ? 'Não encontramos sua busca!' : 'Você concluiu todas as suas entregas!',
                                                  style: GoogleFonts.roboto(fontSize: 18, fontWeight: FontWeight.w500),
                                                ),
                                                if (pesquisa) Text(state.search),
                                                if (pesquisa) const SizedBox(height: 10),
                                                if (pesquisa && state.reallyEmpty)
                                                  ElevatedButton(
                                                    style: ButtonStyle(
                                                      // shape: WidgetStateProperty
                                                      //     .all<
                                                      //         RoundedRectangleBorder>(
                                                      //   RoundedRectangleBorder(
                                                      //     borderRadius:
                                                      //         BorderRadius
                                                      //             .circular(
                                                      //                 8),
                                                      //   ),
                                                      // ),
                                                      shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                                                        RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                                                      ),
                                                      backgroundColor:
                                                      //     WidgetStateProperty
                                                      //         .all<
                                                      //             Color>(
                                                      //   ColorsCustom
                                                      //       .customOrange,
                                                      // ),
                                                      WidgetStateProperty.all<Color>(ColorsCustom.customOrange),
                                                    ),
                                                    onPressed: () {
                                                      controller.setSearch('');
                                                    },
                                                    child: const Padding(
                                                      padding: EdgeInsets.only(top: 18, bottom: 18, left: 40, right: 40),
                                                      child: Text('LIMPAR PESQUISA', style: TextStyle(fontWeight: FontWeight.w800, color: Colors.white)),
                                                    ),
                                                  ),
                                                if (!state.reallyEmpty)
                                                  Text('Tente verificar em outras abas:', textAlign: TextAlign.center, style: GoogleFonts.roboto(fontSize: 18)),
                                                if (!state.reallyEmpty)
                                                  Text(
                                                    state.statusComAtividades.join(', '),
                                                    textAlign: TextAlign.center,
                                                    style: GoogleFonts.roboto(color: ColorsCustom.customOrange, fontSize: 16),
                                                  ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      )
                                      : SingleChildScrollView(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            if (state.deslocamentosColeta.isNotEmpty) ...[
                                              ...List.generate(state.deslocamentosColeta.length, (index) {
                                                final deslocamento = state.deslocamentosColeta[index];
                                                return CustomFcmViewHome(deslocamento: deslocamento);
                                              }),
                                            ],
                                            CustomListviewHome(controller: controller, atividadeOffline: atividadeOffline, position: widget.position),
                                          ],
                                        ),
                                      ),
                            ),
                          ),
                        ],
                      ),
            );
          },
        );
      },
    );
  }
}
