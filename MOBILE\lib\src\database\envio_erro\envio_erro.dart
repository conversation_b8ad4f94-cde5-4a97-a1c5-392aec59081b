import 'dart:convert';

import '../offline_request/offline_request_database.dart';
import '../offline_request/offline_request_hive.dart';

Future enviarErro({
  required bool erro,
  required String metodo,
  required Map<String, dynamic> jsonBody,
}) async {
  final data = OfflineRequest.novo(
    endpoint: '/servicos/gravarlog',
    method: 'POST',
    body: {
      "Erro": erro,
      "Metodo": metodo,
      "JsonBody": jsonEncode(
        jsonBody,
      ),
    },
    headers: {},
    fileName: null,
    fileBytes: null,
    idAtividade: 0,
    enviado: false,
    fileLink: null,
  );
  await OfflineRequestDatabase.instance.addData(data);
}
