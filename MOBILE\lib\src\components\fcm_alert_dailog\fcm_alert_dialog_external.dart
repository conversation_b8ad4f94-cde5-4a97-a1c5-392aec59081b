// import 'package:countly_flutter/countly_flutter.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:octalog/src/database/offline_request/offline_request_hive.dart';

import '../../../fcm_files/main_fcm.dart';
import '../../database/offline_request/offline_request_database.dart';
import '../../helpers/gps/gps_contract.dart';
import '../../helpers/login/login.dart';
import '../../helpers/web_connector.dart';
import 'fcm_external/fcm_external_login.dart';
import 'models/fcm_deslocamento_model.dart';

class FcmExternalFirebase {
  static final instance = FcmExternalFirebase._();

  FcmExternalFirebase._() {
    FirebaseMessaging.onMessage.listen(showFlutterNotification);
  }

  Future<FcmDeslocamentoModel?> getDeslocamento(int deslocamentoId) async {
    try {
      final conn = WebConnector();
      final imei = await conn.getImei();
      final position =
          await GpsHelperContract.instance.updateAndGetLastPosition();
      final fcmKey = FmcExternalLogin.fcmKey;
      final token = Login.instance.usuarioLogado?.token ?? '';

      final data = {
        'deslocamentoID': deslocamentoId,
        'token': token,
        'imei': imei,
        'latitude': position?.latitude,
        'longitude': position?.longitude,
        'fcmKey': fcmKey,
      };
      final response = await conn.get(
        '/deslocamento',
        queryParameters: data,
      );
      try {
        final deslocamento = FcmDeslocamentoModel.fromMap(response.data);
        return deslocamento;
      } catch (e) {
        // Countly.recordDartError(
        //   Exception(
        //     'ERRO TRATAR DESLOCAMENTO - $e - ${response.statusCode}: ${response.data}',
        //   ),
        //   s,
        // );

        return null;
      }
    } catch (e) {
      // Countly.recordDartError(
      //   Exception('ERRO ENVIAR DADOS DESLOCAMENTO - $e'),
      //   s,
      // );

      return null;
    }
  }

  Future<void> deslocamentoInicio(FcmDeslocamentoModel info) async {
    final data = OfflineRequest.novo(
      endpoint: '/deslocamento/inicio',
      method: 'PUT',
      body: info.toMap(),
      headers: {
        'FCMKey': FmcExternalLogin.fcmKey,
      },
      fileName: null,
      fileBytes: null,
      idAtividade: 0,
      enviado: false,
      fileLink: null,
    );
    await OfflineRequestDatabase.instance.addData(data);
  }

  Future<void> deslocamentoChegada(FcmDeslocamentoModel info) async {
    final data = OfflineRequest.novo(
      endpoint: '/deslocamento/chegada',
      method: 'PUT',
      body: info.toMap(),
      headers: {
        'FCMKey': FmcExternalLogin.fcmKey,
      },
      fileName: null,
      fileBytes: null,
      idAtividade: 0,
      enviado: false,
      fileLink: null,
    );
    await OfflineRequestDatabase.instance.addData(data);
  }

  Future<void> deslocamentoFinalizar(FcmDeslocamentoModel info) async {
    final data = OfflineRequest.novo(
      endpoint: '/deslocamento/finalizar',
      method: 'PUT',
      body: info.toMap(),
      headers: {
        'FCMKey': FmcExternalLogin.fcmKey,
      },
      fileName: null,
      fileBytes: null,
      idAtividade: 0,
      enviado: false,
      fileLink: null,
    );
    await OfflineRequestDatabase.instance.addData(data);
  }
}
