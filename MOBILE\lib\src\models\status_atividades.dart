import 'package:map_fields/map_fields.dart';

class StatusAtividades {
  List<StatusAtividadesChild> ocorrenciasInicio;
  List<StatusAtividadesChild> ocorrenciasChegada;
  List<StatusAtividadesChild> problemasEntregas;
  StatusAtividades({
    required this.ocorrenciasInicio,
    required this.ocorrenciasChegada,
    required this.problemasEntregas,
  });

  factory StatusAtividades.fromJson(Map<String, dynamic> json) {
    final f = MapFields.load(json);
    return StatusAtividades(
      ocorrenciasInicio: f
          .getList<Map<String, dynamic>>('OcorrenciaInicio')
          .map((e) => StatusAtividadesChild.fromJson(e))
          .toList(),
      ocorrenciasChegada: f
          .getList<Map<String, dynamic>>('OcorrenciaChegada')
          .map((e) => StatusAtividadesChild.fromJson(e))
          .toList(),
      problemasEntregas: f
          .getList<Map<String, dynamic>>('ProblemaEntrega')
          .map((e) => StatusAtividadesChild.fromJson(e))
          .toList(),
    );
  }
}

class StatusAtividadesChild {
  final int idStatusAtividade;
  final String nome;
  final bool finalizador;
  final bool receita;
  final bool acareacao;
  final bool sacObrigatorio;
  StatusAtividadesChild({
    required this.idStatusAtividade,
    required this.nome,
    required this.finalizador,
    required this.receita,
    required this.acareacao,
    required this.sacObrigatorio,
  });

  factory StatusAtividadesChild.fromJson(Map<String, dynamic> json) {
    final f = MapFields.load(json);
    return StatusAtividadesChild(
      idStatusAtividade: f.getInt('IDStatusAtividade', -1),
      nome: f.getString('Nome', ''),
      finalizador: f.getBool('Finalizador', false),
      receita: f.getBool('Receita', false),
      acareacao: f.getBool('Acareacao', false),
      sacObrigatorio: f.getBool('SacObrigatorio', false),
    );
  }
}
