import 'dart:convert';

import 'package:hive/hive.dart';
import 'package:octalog/src/models/user.dart';
import 'package:map_fields/map_fields.dart';

class UsuarioDataHive {
  final String nomeCompleto;
  final String telefone;
  final String email;
  final String uf;
  final String foto;
  final String usuario;
  final String senha;
  final String? token;
  final bool atualizarCadastro;
  final String? cpf;
  final int idTipoAgenteUberizado;
  final bool permiteTransferenciaMobile;
  UsuarioDataHive({
    required this.nomeCompleto,
    required this.telefone,
    required this.email,
    required this.uf,
    required this.foto,
    required this.cpf,
    required this.usuario,
    required this.senha,
    required this.atualizarCadastro,
    required this.idTipoAgenteUberizado,
    required this.permiteTransferenciaMobile,
    this.token,
  });

  factory UsuarioDataHive.fromUserData(UserData userData) {
    return UsuarioDataHive(
      nomeCompleto: userData.nomeCompleto,
      telefone: userData.telefone,
      email: userData.email,
      uf: userData.uf,
      foto: userData.foto,
      cpf: userData.cpf,
      usuario: userData.usuario,
      senha: userData.senha,
      token: userData.token,
      atualizarCadastro: userData.atualizarCadastro,
      idTipoAgenteUberizado: userData.idTipoAgenteUberizado,
      permiteTransferenciaMobile: userData.permiteTransferenciaMobile,
    );
  }

  UserData toUserData() {
    return UserData(
      nomeCompleto: nomeCompleto,
      telefone: telefone,
      email: email,
      uf: uf,
      foto: foto,
      cpf: cpf ?? '',
      userLogin: UserLogin(
        usuario: usuario,
        senha: senha,
        token: token,
        atualizarCadastro: atualizarCadastro,
      ),
      idTipoAgenteUberizado: idTipoAgenteUberizado,
      permiteTransferenciaMobile: permiteTransferenciaMobile,
    );
  }

  factory UsuarioDataHive.fromHiveMap(Map<String, dynamic> map) {
    final f = MapFields.load(map);
    return UsuarioDataHive(
      nomeCompleto: f.getString('nomeCompleto', ''),
      telefone: f.getString('telefone', ''),
      email: f.getString('email', ''),
      uf: f.getString('uf', ''),
      foto: f.getString('foto', ''),
      cpf: f.getString('cpf', ''),
      usuario: f.getString('usuario', ''),
      senha: f.getString('senha', ''),
      token: f.getString('token', ''),
      atualizarCadastro: f.getBool('atualizarCadastro', false),
      idTipoAgenteUberizado: f.getInt('idTipoAgenteUberizado', 0),
      permiteTransferenciaMobile:
          f.getBool('permiteTransferenciaMobile', false),
    );
  }

  Map<String, dynamic> toHiveMap() {
    return {
      'nomeCompleto': nomeCompleto,
      'telefone': telefone,
      'email': email,
      'uf': uf,
      'foto': foto,
      'cpf': cpf,
      'usuario': usuario,
      'senha': senha,
      'token': token,
      'atualizarCadastro': atualizarCadastro,
      'idTipoAgenteUberizado': idTipoAgenteUberizado,
      'permiteTransferenciaMobile': permiteTransferenciaMobile,
    };
  }
}

class LoginHive {
  static LoginHive instance = LoginHive();
  final box = Hive.box<String>('login_map');
  Future<void> save(
    UserData userData,
  ) async {
    final usuario = UsuarioDataHive.fromUserData(userData);
    await box.put(0, json.encode(usuario.toHiveMap()));
  }

  Future<void> clear() async {
    await box.clear();
  }

  Future<UserData?> read() async {
    final response = box.get(0);
    return response == null
        ? null
        : UsuarioDataHive.fromHiveMap(json.decode(response)).toUserData();
  }
}
