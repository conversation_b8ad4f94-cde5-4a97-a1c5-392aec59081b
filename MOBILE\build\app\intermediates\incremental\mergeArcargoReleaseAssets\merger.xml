<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="com.google.mlkit:face-detection:16.1.7" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\137683c38d0641808b56059b5fe37a10\transformed\jetified-face-detection-16.1.7\assets"><file name="models_bundled/BCLjoy_200.emd" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\137683c38d0641808b56059b5fe37a10\transformed\jetified-face-detection-16.1.7\assets\models_bundled\BCLjoy_200.emd"/><file name="models_bundled/BCLlefteyeclosed_200.emd" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\137683c38d0641808b56059b5fe37a10\transformed\jetified-face-detection-16.1.7\assets\models_bundled\BCLlefteyeclosed_200.emd"/><file name="models_bundled/BCLrighteyeclosed_200.emd" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\137683c38d0641808b56059b5fe37a10\transformed\jetified-face-detection-16.1.7\assets\models_bundled\BCLrighteyeclosed_200.emd"/><file name="models_bundled/blazeface.tfl" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\137683c38d0641808b56059b5fe37a10\transformed\jetified-face-detection-16.1.7\assets\models_bundled\blazeface.tfl"/><file name="models_bundled/contours.tfl" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\137683c38d0641808b56059b5fe37a10\transformed\jetified-face-detection-16.1.7\assets\models_bundled\contours.tfl"/><file name="models_bundled/fssd_25_8bit_gray_v2.tflite" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\137683c38d0641808b56059b5fe37a10\transformed\jetified-face-detection-16.1.7\assets\models_bundled\fssd_25_8bit_gray_v2.tflite"/><file name="models_bundled/fssd_25_8bit_v2.tflite" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\137683c38d0641808b56059b5fe37a10\transformed\jetified-face-detection-16.1.7\assets\models_bundled\fssd_25_8bit_v2.tflite"/><file name="models_bundled/fssd_anchors_v2.pb" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\137683c38d0641808b56059b5fe37a10\transformed\jetified-face-detection-16.1.7\assets\models_bundled\fssd_anchors_v2.pb"/><file name="models_bundled/fssd_anchors_v5.pb" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\137683c38d0641808b56059b5fe37a10\transformed\jetified-face-detection-16.1.7\assets\models_bundled\fssd_anchors_v5.pb"/><file name="models_bundled/fssd_medium_8bit_gray_v5.tflite" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\137683c38d0641808b56059b5fe37a10\transformed\jetified-face-detection-16.1.7\assets\models_bundled\fssd_medium_8bit_gray_v5.tflite"/><file name="models_bundled/fssd_medium_8bit_v5.tflite" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\137683c38d0641808b56059b5fe37a10\transformed\jetified-face-detection-16.1.7\assets\models_bundled\fssd_medium_8bit_v5.tflite"/><file name="models_bundled/LMprec_600.emd" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\137683c38d0641808b56059b5fe37a10\transformed\jetified-face-detection-16.1.7\assets\models_bundled\LMprec_600.emd"/><file name="models_bundled/MFT_fssd_accgray.pb" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\137683c38d0641808b56059b5fe37a10\transformed\jetified-face-detection-16.1.7\assets\models_bundled\MFT_fssd_accgray.pb"/><file name="models_bundled/MFT_fssd_fastgray.pb" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\137683c38d0641808b56059b5fe37a10\transformed\jetified-face-detection-16.1.7\assets\models_bundled\MFT_fssd_fastgray.pb"/></source></dataSet><dataSet config="com.google.mlkit:barcode-scanning:17.3.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d17889e797a41f531d22754be8eceb74\transformed\jetified-barcode-scanning-17.3.0\assets"><file name="mlkit_barcode_models/barcode_ssd_mobilenet_v1_dmp25_quant.tflite" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d17889e797a41f531d22754be8eceb74\transformed\jetified-barcode-scanning-17.3.0\assets\mlkit_barcode_models\barcode_ssd_mobilenet_v1_dmp25_quant.tflite"/><file name="mlkit_barcode_models/oned_auto_regressor_mobile.tflite" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d17889e797a41f531d22754be8eceb74\transformed\jetified-barcode-scanning-17.3.0\assets\mlkit_barcode_models\oned_auto_regressor_mobile.tflite"/><file name="mlkit_barcode_models/oned_feature_extractor_mobile.tflite" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d17889e797a41f531d22754be8eceb74\transformed\jetified-barcode-scanning-17.3.0\assets\mlkit_barcode_models\oned_feature_extractor_mobile.tflite"/></source></dataSet><dataSet config=":url_launcher_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\url_launcher_android\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":system_info_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\system_info_plus\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":sqlite3_flutter_libs" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\sqlite3_flutter_libs\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":sqflite_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\sqflite_android\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":rive_common" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\rive_common\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":permission_handler_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\permission_handler_android\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":path_provider_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\path_provider_android\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":package_info_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\package_info_plus\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":onesignal_flutter" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\onesignal_flutter\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":google_mlkit_commons" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\google_mlkit_commons\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":google_mlkit_face_detection" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\google_mlkit_face_detection\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":flutter_webrtc" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\flutter_webrtc\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":flutter_ringtone_player" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\flutter_ringtone_player\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":flutter_plugin_android_lifecycle" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\flutter_plugin_android_lifecycle\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":image_picker_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":flutter_pdfview" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\flutter_pdfview\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":flutter_keyboard_visibility" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\flutter_keyboard_visibility\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":firebase_core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\firebase_core\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":firebase_remote_config" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\firebase_remote_config\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":firebase_messaging" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":file_picker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\file_picker\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":device_info_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\device_info_plus\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":connectivity_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\connectivity_plus\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":camera_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\camera_android\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":geolocator_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\geolocator_android\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":webview_flutter_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\webview_flutter_android\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":speech_to_text" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\speech_to_text\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":shared_preferences_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\shared_preferences_android\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":mobile_scanner" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\mobile_scanner\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":in_app_review" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\in_app_review\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":flutter_activity_recognition" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":android_id" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\android_id\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":airplane_mode_checker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\airplane_mode_checker\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\android\app\src\main\assets"/></dataSet><dataSet config="arcargo" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\android\app\src\arcargo\assets"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\android\app\src\release\assets"/></dataSet><dataSet config="variant" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\android\app\src\arcargoRelease\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\app\intermediates\shader_assets\arcargoRelease\compileArcargoReleaseShaders\out"/></dataSet></merger>