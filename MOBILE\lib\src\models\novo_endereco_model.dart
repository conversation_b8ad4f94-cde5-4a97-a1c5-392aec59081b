import 'package:map_fields/map_fields.dart';

class NovoEnderecoModel {
  String apelido;
  String endereco;
  String numero;
  String bairro;
  String cidade;
  String cep;
  String uf;

  NovoEnderecoModel(
      {required this.apelido,
      required this.endereco,
      required this.numero,
      required this.bairro,
      required this.cidade,
      required this.cep,
      required this.uf});

  factory NovoEnderecoModel.fromJson(Map<String, dynamic> json) {
    final MapFields mapFields = MapFields.load(json);
    return NovoEnderecoModel(
      // apelido: json['Apelido'] as String,
      // endereco: json['Endereco'] as String,
      // numero: json['Numero'] as String,
      // bairro: json['Bairro'] as String,
      // cidade: json['Cidade'] as String,
      // cep: json['Cep'] as String,
      // uf: json['Uf'] as String,
      apelido: mapFields.getString('Apelido', ''),
      endereco: mapFields.getString('Endereco', ''),
      numero: mapFields.getString('Numero', ''),
      bairro: mapFields.getString('Bairro', ''),
      cidade: mapFields.getString('Cidade', ''),
      cep: mapFields.getString('Cep', ''),
      uf: mapFields.getString('Uf', ''),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'Apelido': apelido.toString(),
      'Endereco': endereco.toString(),
      'Numero': numero.toString(),
      'Bairro': bairro.toString(),
      'Cidade': cidade.toString(),
      'Cep': cep.toString(),
      'UF': uf.toString(),
    };
  }
}
