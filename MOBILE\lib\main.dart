import 'dart:async';

//import 'package:firebase_core/firebase_core.dart';
// import 'package:firebase_messaging/firebase_messaging.dart';
//import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:map_fields/map_fields.dart';
import 'package:octalog/one_signal_wrapper.dart';
import 'package:octalog/src/database/firebase_background/firebase_drift.dart';
import 'package:octalog/src/database/log_database/log_database.dart';
import 'package:octalog/src/database/log_database/status_agente_database.dart';
import 'package:octalog/src/utils/function_info_device.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqlite3_flutter_libs/sqlite3_flutter_libs.dart';

import 'app_widget.dart';
//import 'fcm_files/main_fcm.dart';
//import 'firebase_options.dart';
import 'src/config/flavor_helper.dart';
import 'src/database/hash_buscar/hash_buscar_database.dart';
import 'src/database/offline_request/offline_request_drift_model.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize flavor configuration
  FlavorHelper.initializeFlavor();

  await OneSignalService().init();

  // Run independent initializations in parallel
  await Future.wait([
    applyWorkaroundToOpenSqlite3OnOldAndroidVersions(),
    //Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform),
    Hive.initFlutter((await getApplicationDocumentsDirectory()).path),
    deviceInfoStatic(),
  ]);

  // Initialize databases
  OfflineRequestDatabaseDrift.instance;
  FirebaseDatabaseDrift.instance;
  
  // Configure error widget if in debug mode
  if (kDebugMode) {
    ErrorWidget.builder = (FlutterErrorDetails details) {
      return Container(
        color: Colors.red.withOpacity(.5),
        child: Center(
          child: Text(
            'Ocorreu um erro inesperado!\nEntre em contato com o suporte.\n\n${details.exception.toString()}',
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 30,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
      );
    };
  }

  // Initialize remaining services
  MapFieldsSettings.instance.setLanguage(MapFieldsLanguages.ptBr);
  await Hive.openBox<String>('login_map');
  await HashBuscarDatabase.instance.initHashBuscar();
  await LogDatabase.instance.initLogs();
  await StatusAgenteDatabase.instance.initStatusAgente();

  runApp(const AppWidget());
}
