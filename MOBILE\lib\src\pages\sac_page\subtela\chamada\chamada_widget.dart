import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_ringtone_player/flutter_ringtone_player.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:octalog/src/database/config_blob/config_database.dart';
import 'package:octalog/src/helpers/web_socket/hub_helper.dart';
import 'package:signalr_core/signalr_core.dart';

class ChamadaPage extends StatefulWidget {
  final String? remoteOffer;
  final UsuarioHub usuarioHub;
  const ChamadaPage({super.key, this.remoteOffer, required this.usuarioHub});

  @override
  State<ChamadaPage> createState() => _ChamadaPageState();
}

class _ChamadaPageState extends State<ChamadaPage> {
  bool ligacao = true;
  final localRenderer = RTCVideoRenderer();
  final remoteRenderer = RTCVideoRenderer();
  MediaStream? localStream;
  RTCPeerConnection? peerConnection;
  late HubConnection hubConnection;
  String? remoteOffer;
  List<RTCIceCandidate> iceCandidatesQueue = [];
  bool vivaVoz = false;
  Timer? timer;
  final FlutterRingtonePlayer ringtonePlayer = FlutterRingtonePlayer();
  int elapsedTime = 0;

  void executeChamado() {
    ringtonePlayer.play(
      android: AndroidSounds.ringtone,
      ios: IosSounds.glass,
      looping: true,
      volume: 1.0,
    );
  }

  @override
  void initState() {
    super.initState();
    initializeRenderers();
    initSignalR();
    executeChamado();
  }

  Future<void> initializeRenderers() async {
    await localRenderer.initialize();
    await remoteRenderer.initialize();
  }

  Future<void> initSignalR() async {
    final config = await ConfigDatabase.instance.getConfig();
    hubConnection = HubHelper.getInstance('${config.linkHub}/hub-call').hub;
    if (widget.remoteOffer != null) {
      final offerMap = jsonDecode(widget.remoteOffer!);
      remoteOffer = jsonEncode(offerMap);
    }
    await hubConnection.start();
    hubConnection.on("ReceberOfertaChamada", _onReceiveOffer);
    hubConnection.on("ReceberCandidatoICE", _onReceiveIceCandidate);

    try {
      log("Conectado ao SignalR");
    } catch (e) {
      log("Erro ao conectar ao SignalR: $e");
    }
  }

  void fechartela(List<Object?>? arguments) async {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else {
      log("Error: Unable to pop navigation due to invalid context");
    }

    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Chamada encerrada'),
          duration: Duration(seconds: 2),
        ),
      );
    } catch (_) {}
  }

  void _onReceiveOffer(List<Object?>? arguments) {
    if (arguments == null || arguments.isEmpty) return;

    final offerMap = jsonDecode(arguments[0] as String);
    remoteOffer = jsonEncode(offerMap);
    log("Oferta recebida: $remoteOffer");
  }

  void _onReceiveIceCandidate(List<Object?>? arguments) async {
    if (arguments == null || arguments.isEmpty) return;

    final candidateMap = jsonDecode(arguments[0] as String);
    final candidate = RTCIceCandidate(
      candidateMap['candidate'],
      candidateMap['sdpMid'],
      candidateMap['sdpMLineIndex'],
    );

    if (peerConnection != null) {
      await peerConnection!.addCandidate(candidate);
      log("Candidato ICE recebido e adicionado.");
    } else {
      iceCandidatesQueue.add(candidate);
      log("Candidato ICE armazenado na fila.");
    }
  }

  Future<void> _acceptCall() async {
    try {
      await _initializePeerConnection();
      await _setLocalStream();
      await _handleRemoteOffer();
      await _processIceCandidates();

      ligacao = false;
      setState(() {});
      log("Chamada aceita com sucesso.");
    } catch (e) {
      log("Erro ao aceitar chamada de voz: $e");
    }
  }

  Future<void> _initializePeerConnection() async {
    peerConnection = await createPeerConnection({
      "iceServers": [
        {"urls": "stun:stun.l.google.com:19302"},
      ],
    });

    peerConnection?.onIceCandidate = (RTCIceCandidate candidate) {
      log("Enviando candidato ICE...");
      hubConnection.invoke("EnviarCandidatoICE", args: [
        {"id": widget.usuarioHub.id, "candidate": jsonEncode(candidate.toMap())}
      ]).catchError((e) {
        log("Erro ao enviar candidato ICE: $e");
      });
    };

    peerConnection?.onIceConnectionState = (state) {
      if (state == RTCIceConnectionState.RTCIceConnectionStateDisconnected) {
        fechartela(null);
      }
    };

    peerConnection?.onTrack = (RTCTrackEvent event) {
      if (event.streams.isNotEmpty) {
        remoteRenderer.srcObject = event.streams.first;
      }
    };

    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        elapsedTime++;
      });
    });
  }

  Future<void> _setLocalStream() async {
    localStream = await navigator.mediaDevices
        .getUserMedia({'video': false, 'audio': true});

    localStream?.getTracks().forEach((track) {
      peerConnection?.addTrack(track, localStream!);
    });
  }

  Future<void> _handleRemoteOffer() async {
    if (remoteOffer != null) {
      final offerMap = jsonDecode(remoteOffer!);
      if (offerMap != null) {
        await peerConnection?.setRemoteDescription(
          RTCSessionDescription(offerMap['sdp'], offerMap['type']),
        );
        RTCSessionDescription answer = await peerConnection!.createAnswer();
        await peerConnection?.setLocalDescription(answer);

        log("Enviando resposta de chamada...");
        hubConnection.invoke("ResponderChamada", args: [
          {
            "id": widget.usuarioHub.id,
            "resposta": jsonEncode(answer.toMap()),
            "aceita": true
          }
        ]);
      }
    }
  }

  Future<void> _processIceCandidates() async {
    for (var candidate in iceCandidatesQueue) {
      await peerConnection?.addCandidate(candidate);
    }
    iceCandidatesQueue.clear();
  }

  @override
  void dispose() {
    timer?.cancel();
    localRenderer.dispose();
    remoteRenderer.dispose();
    localStream?.dispose();
    peerConnection?.close();
    peerConnection?.dispose();
    ringtonePlayer.stop();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    String formatTime(int seconds) {
      int hours = seconds ~/ 3600;
      int minutes = (seconds % 3600) ~/ 60;
      int remainingSeconds = seconds % 60;
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Chamada'),
        centerTitle: true,
        automaticallyImplyLeading: false,
        actions: const [],
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Visibility(
              visible: ligacao,
              replacement: Column(
                children: [
                  const Text(
                    'Chamada em andamento',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    formatTime(elapsedTime),
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  const SizedBox(height: 30),
                ],
              ),
              child: const Text(
                'Atendente',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Container(
              width: 200,
              height: 200,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                image: DecorationImage(
                  image: AssetImage('assets/sac/atendete.png'),
                  fit: BoxFit.fill,
                ),
              ),
            ),
            Visibility(
              visible: ligacao,
              replacement: Column(
                children: [
                  const SizedBox(height: 20),
                  const Text(
                    'Você está em chamada com',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  Text(
                    widget.usuarioHub.usuario,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ],
              ),
              child: Text(
                '${widget.usuarioHub.usuario} esta te ligando',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
            const SizedBox(height: 30),
            const SizedBox(height: 30),
          ],
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 30),
        child: SizedBox(
          height: 80,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Expanded(
                flex: 1,
                child: FloatingActionButton(
                  onPressed: () {
                    if (!ligacao) {
                      hubConnection.invoke("DesligarChamada", args: [
                        widget.usuarioHub.id,
                      ]);
                    } else {
                      hubConnection.invoke("ResponderChamada", args: [
                        {
                          "id": widget.usuarioHub.id,
                          "resposta": '',
                          "aceita": false
                        }
                      ]);
                    }
                    Navigator.pop(context);
                  },
                  backgroundColor: Colors.red,
                  child: const Icon(
                    Icons.call_end,
                    color: Colors.white,
                  ),
                ),
              ),
              Visibility(
                visible: ligacao,
                child: const SizedBox(width: 20),
              ),
              Visibility(
                visible: ligacao,
                child: Expanded(
                  flex: 2,
                  child: FloatingActionButton(
                    onPressed: () async {
                      ringtonePlayer.stop();
                      await _acceptCall();
                    },
                    backgroundColor: Colors.green,
                    child: const Icon(
                      Icons.call,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
