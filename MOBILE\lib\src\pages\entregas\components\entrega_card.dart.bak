import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/models_new/endereco_new.dart';
import 'package:octalog/src/pages/entrega_newpages/controller/entrega_new_etapa.dart';

import '../../../components/image_perfil/image_perfil.dart';
import '../../../components/reentrega_widget/reentrega_widget.dart';
import '../../../models_new/cliente_new.dart';
import '../../../utils/colors.dart';
import '../../home/<USER>/widget_acareacao/widget_acareacao.dart';

class EntregaCard extends StatelessWidget {
  final ClienteNew cliente;
  final EnderecoNew atividade;
  final Function? onTap;
  final EntregaNewEtapa etapa;

  const EntregaCard({
    super.key,
    required this.cliente,
    required this.atividade,
    required this.etapa,
    this.onTap,
  });
// 321898222
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (onTap != null) {
          onTap!();
        }
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 22),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  cliente.nomeCliente,
                  style: GoogleFonts.roboto(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ],
            ),
            Visibility(
              visible: cliente.mensagem.isNotEmpty,
              child: Column(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(5),
                    ),
                    padding: const EdgeInsets.all(5),
                    width: double.infinity,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.only(left: 5, right: 5),
                            child: Text(
                              cliente.mensagem,
                              style: GoogleFonts.roboto(
                                fontSize: 14,
                                color: ColorsCustom.customBlack,
                              ),
                              overflow: TextOverflow.visible,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 5),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: ImagePerfil(
                    url: cliente.logo ?? '',
                    iniciais: cliente.iniciais,
                    fontSize: 25,
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (cliente.complemento.isNotEmpty)
                        const SizedBox(height: 3),
                      if (cliente.complemento.isNotEmpty)
                        Text(
                          cliente.complemento,
                          style: GoogleFonts.roboto(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: ColorsCustom.customOrange,
                          ),
                        ),
                      const SizedBox(height: 5),
                      Column(
                        children:
                            List.generate(cliente.volumes.length, (index) {
                          final codRastreio = cliente.volumes[index];
                          return Column(
                            children: [
                              Text(
                                codRastreio.os,
                                style: GoogleFonts.roboto(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: ColorsCustom.customBlack,
                                ),
                              ),
                            ],
                          );
                        }),
                      ),
                      const SizedBox(height: 5),
                      Padding(
                        padding: const EdgeInsets.only(left: 5),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Text(
                            //   '${cliente.pedidosLength} Pedido${cliente.pedidosLength > 1 ? 's' : ''}',
                            //   style: GoogleFonts.roboto(
                            //     fontSize: 18,
                            //     fontWeight: FontWeight.normal,
                            //     color: ColorsCustom.customGrey,
                            //   ),
                            // ),
                            if (cliente.pontoReferencia.isNotEmpty)
                              const SizedBox(height: 3),
                            if (cliente.pontoReferencia.isNotEmpty)
                              Text(
                                'Ponto de referência: ${cliente.pontoReferencia}',
                                style: GoogleFonts.roboto(
                                  fontSize: 18,
                                  fontWeight: FontWeight.normal,
                                  color: ColorsCustom.customGrey,
                                ),
                              ),
                            const SizedBox(height: 5),
                            // Text(
                            //   cliente.statusString.join(', '),
                            //   style: GoogleFonts.roboto(
                            //     fontSize: 16,
                            //     fontWeight: FontWeight.normal,
                            //     color: ColorsCustom.customOrange,
                            //   ),
                            // ),
                          ],
                        ),
                      ),
                      ReentregaWidget(
                        scale: 0.8,
                        tags: cliente.tags,
                      ),
                      Visibility(
                        visible: cliente.acareacao,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                            vertical: 5,
                          ),
                          child: GestureDetector(
                            onTap: () async {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => AcareacaoWidget(
                                    cliente: cliente,
                                  ),
                                ),
                              );
                              // return asuka.Asuka.showDialog(
                              //   builder: (ctx) => AcareacaoWidget(
                              //     cliente: cliente,
                              //   ),
                              // );
                            },
                            child: const Text(
                              'Informações da acareação',
                              style: TextStyle(
                                color: ColorsCustom.customOrange,
                                fontSize: 15,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Visibility(
                  visible: etapa == EntregaNewEtapa.deslocando ||
                      etapa == EntregaNewEtapa.inicio,
                  child: const Icon(
                    Icons.more_vert,
                    size: 30,
                    color: ColorsCustom.customGrey,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
