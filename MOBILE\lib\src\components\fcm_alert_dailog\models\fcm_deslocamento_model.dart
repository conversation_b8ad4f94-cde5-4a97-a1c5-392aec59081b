import 'package:map_fields/map_fields.dart';

class FcmDeslocamentoModel {
  final int idDeslocamento;
  final DateTime dataHora;
  final double? latitude;
  final double? longitude;

  FcmDeslocamentoModel({
    required this.idDeslocamento,
    required this.dataHora,
    required this.latitude,
    required this.longitude,
  });

  factory FcmDeslocamentoModel.fromMap(Map<String, dynamic> map) {
    final f = MapFields.load(map);
    return FcmDeslocamentoModel(
      idDeslocamento: f.getInt('idDeslocamento', 0),
      dataHora: f.getDateTime('dataHora', DateTime.now()),
      latitude: f.getDoubleNullable('latitude'),
      longitude: f.getDoubleNullable('longitude'),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      "iDDeslocamento": idDeslocamento,
      "DataHora": dataHora.toIso8601String(),
      "Latitude": latitude,
      "Longitude": longitude,
    };
  }
}
