import 'package:flutter/material.dart';

import '../pages/home/<USER>';
import './../utils/colors.dart';

class IconPadrao extends StatelessWidget {
  final bool? isBack;
  const IconPadrao({
    super.key,
    this.isBack,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40,
      width: 40,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: ColorsCustom.customOrange.withOpacity(0.1),
      ),
      child: IconButton(
        icon: const Icon(
          Icons.arrow_back,
          color: ColorsCustom.customOrange,
        ),
        onPressed: isBack != null
            ? () {
                if (isBack ?? true) {
                  if (Navigator.canPop(context)) {
                    Navigator.pop(context);
                    return;
                  }
                } else {
                  Navigator.pushAndRemoveUntil(
                    context,
                    MaterialPageRoute(
                      builder: (_) => const Home(
                        enterAtividade: false,
                      ),
                    ),
                    (route) => false,
                  );
                }
              }
            : () {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: const Text(
                      'Você não pode voltar!',
                      style: TextStyle(
                        color: ColorsCustom.customOrange,
                      ),
                    ),
                    action: SnackBarAction(
                      label: 'OK',
                      textColor: ColorsCustom.customOrange,
                      onPressed: () =>
                          ScaffoldMessenger.of(context).hideCurrentSnackBar(),
                    ),
                  ),
                );
              },
      ),
    );
  }
}
