import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import '../loading_ls/loading_ls.dart';

class ButtonLsCustom extends StatefulWidget {
  final bool isLoading;
  final String text;
  final Function()? onPressed;
  final Color? colorBackground;
  final String? message;
  final double? horizontal;
  final bool disabled;
  
  const ButtonLsCustom({
    super.key,
    this.isLoading = false,
    required this.text,
    this.onPressed,
    this.colorBackground,
    this.message,
    this.horizontal,
    this.disabled = false,
  });

  @override
  State<ButtonLsCustom> createState() => _ButtonLsCustomState();
}

class _ButtonLsCustomState extends State<ButtonLsCustom> {
  bool running = false;

  Function()? get onPressed {
    if (widget.onPressed == null) return null;
    if (widget.isLoading) return null;
    return run;
  }

  Future<void> run() async {
    if (running) return;
    running = true;
    try {
      widget.onPressed!();
    } catch (_) {}
    await Future.delayed(const Duration(milliseconds: 200));
    running = false;
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 56,
      child: ElevatedButton(
        style: ButtonStyle(
          backgroundColor: WidgetStateProperty.resolveWith<Color>(
            (Set<WidgetState> states) {
              if (states.contains(WidgetState.disabled)) {
                return ThemeColors.customGrey(context);
              }
              return widget.colorBackground ?? ThemeColors.customOrange(context);
            },
          ),
          // 15 de borderRadius
          shape: WidgetStateProperty.all<RoundedRectangleBorder>(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
        onPressed: widget.disabled ? null : funcIfDisabled(
          context,
          widget.message,
          widget.isLoading,
          onPressed,
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: widget.horizontal ?? 0,
          ),
          child: Center(
            child: widget.isLoading
                ? const LoadingLs()
                : Text(
                    widget.text,
                    softWrap: false,
                    style: GoogleFonts.roboto(
                      fontSize: 16,
                      fontWeight: FontWeight.w700,
                      color: ThemeColors.customWhite(context),
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  Function()? funcIfDisabled(
      context, String? message, bool isLoading, Function()? onPressed) {
    if (isLoading) {
      return null;
    }
    if (message != null) {
      return () async {
        await showDialog(
          context: context,
          barrierColor: Colors.black.withOpacity(0.8),
          builder: (context) => AlertDialog(
            title: const Text('Atenção'),
            content: Text(message),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'CONFIRMAR',
                  style: TextStyle(
                    color: context.customOrange,
                  ),
                ),
              ),
            ],
          ),
        );
      };
    }
    return onPressed;
  }
}
