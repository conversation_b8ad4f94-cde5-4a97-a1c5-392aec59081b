import 'dart:convert';

import 'package:hive/hive.dart';
import 'package:map_fields/map_fields.dart';

class PedidosSacModel {
  final int idos;
  final int idStatusAtividade;
  final int idSacAtendimento;
  final List<int> status;
  PedidosSacModel({
    required this.idos,
    required this.idStatusAtividade,
    required this.idSacAtendimento,
    required this.status,
  });

  factory PedidosSacModel.fromJson(Map<String, dynamic> json) {
    final MapFields mapFields = MapFields.load(json);
    return PedidosSacModel(
        idos: mapFields.getInt('IDOS', 0),
        idStatusAtividade: mapFields.getInt('IDStatusAtividade', 0),
        idSacAtendimento: mapFields.getInt('IDSacAtendimento', 0),
        status: mapFields.getList<int>('Status', []));
  }

  Map<String, dynamic> toJson() {
    return {
      'IDOS': idos,
      'IDStatusAtividade': idStatusAtividade,
      'IDSacAtendimento': idSacAtendimento,
    };
  }

  static List<PedidosSacModel> fromJsonWithStatusList(
      List<PedidosSacModel> pedidos) {
    List<PedidosSacModel> expandedList = [];

    for (var pedido in pedidos) {
      for (var statusId in pedido.status) {
        expandedList.add(
          PedidosSacModel(
            idos: pedido.idos,
            idStatusAtividade: statusId,
            idSacAtendimento: pedido.idSacAtendimento,
            status: pedido.status,
          ),
        );
      }
    }

    return expandedList;
  }
}

abstract class PedidosSacDatabase {
  static final instance = HivePedidosSacDatabaseImpl();
  Future<void> setSac(PedidosSacModel hiveSacModel);
  Future<List<PedidosSacModel>> getSac(int idos);
  Future<List<PedidosSacModel>> getAllSac();
  Future<void> deletSac(
    int idos,
    int idStatusAtividade,
    int idSacAtendimento,
  );
  Future<void> deleteAllSac();
}

class HivePedidosSacDatabaseImpl implements PedidosSacDatabase {
  bool _boxIniciada = false;

  late Box _box;

  Future<void> _iniciarBanco() async {
    if (_boxIniciada) return;
    _box = await Hive.openBox('sac_atendimento_baixas');
    _boxIniciada = true;
  }

  HivePedidosSacDatabaseImpl() {
    _iniciarBanco();
  }

  @override
  Future<void> setSac(PedidosSacModel hiveSacModel) async {
    await _iniciarBanco();

    final List<PedidosSacModel> list = await getAllSac();

    if (list.any((element) =>
        element.idos == hiveSacModel.idos &&
        element.idStatusAtividade == hiveSacModel.idStatusAtividade)) {
      return;
    }
    _box.add(jsonEncode(hiveSacModel.toJson()));
  }

  @override
  Future<List<PedidosSacModel>> getSac(int idos) async {
    await _iniciarBanco();
    final List<PedidosSacModel> list = await getAllSac();

    return list.where((element) => element.idos == idos).toList();
  }

  @override
  Future<List<PedidosSacModel>> getAllSac() async {
    await _iniciarBanco();
    final List<String> listString = _box.values.cast<String>().toList();
    return listString
        .map((e) => PedidosSacModel.fromJson(jsonDecode(e)))
        .toList();
  }

  @override
  Future<void> deletSac(
    int idos,
    int idStatusAtividade, [
    int? idSacAtendimento,
  ]) async {
    await _iniciarBanco();
    final List<PedidosSacModel> list = await getAllSac();

    final List<PedidosSacModel> newList = list.where((element) {
      if (idSacAtendimento != null) {
        return element.idos == idos &&
            element.idStatusAtividade == idStatusAtividade &&
            element.idSacAtendimento == idSacAtendimento;
      }
      return element.idos == idos &&
          element.idStatusAtividade == idStatusAtividade;
    }).toList();

    for (var item in newList) {
      _box.deleteAt(_box.values.toList().indexOf(jsonEncode(item.toJson())));
    }
  }

  @override
  Future<void> deleteAllSac() async {
    await _iniciarBanco();
    await _box.clear();
  }
}
