import 'dart:convert';

import 'package:firebase_messaging/firebase_messaging.dart';

import '../../../fcm_files/main_fcm.dart';
import '../log_database/log_database.dart';
import 'firebase_drift.dart';

abstract class FcmDataBase {
  static final instance = FcmDataBaseImpl();

  Future<void> initFcmDb();
  Future<void> saveFcm(RemoteMessage remoteMessage);
  Future<void> deleteFcm(String hashCodeMessage);
  Future<void> shownotification();
}

class FcmDataBaseImpl implements FcmDataBase {
  final database = FirebaseDatabaseDrift.instance;
  bool initialized = false;
  bool error = false;

  @override
  Future<void> initFcmDb() async {
    initialized = true;
    error = false;
  }

  @override
  Future<void> saveFcm(RemoteMessage remoteMessage) async {
    await initFcmDb();
    while (!initialized) {
      await Future.delayed(const Duration(milliseconds: 100));
    }
    if (error) {
      await LogDatabase.instance.logError(
        'Banco de dados FCM',
        '',
        'Erro ao carregar o banco de dados',
        remoteMessage.toMap(),
      );
      return;
    }
    try {
      final data = jsonEncode(remoteMessage.toMap());
      final dataInsert = FirebaseDriftCompanion.insert(
        texto: data,
      );
      await database.into(database.firebaseDrift).insert(dataInsert);
    } catch (_) {
      await LogDatabase.instance.logError(
        'Banco de dados FCM',
        '',
        'Erro ao gravar no banco de dados',
        remoteMessage.toMap(),
      );
    }
  }

  @override
  Future<void> deleteFcm(String hashCodeMessage) async {
    await initFcmDb();
    while (!initialized) {
      await Future.delayed(const Duration(milliseconds: 100));
    }
    if (error) {
      await LogDatabase.instance.logError(
        'Banco de dados FCM',
        '',
        'Erro ao carregar o banco de dados',
        {},
      );
      return;
    }
    final id = int.parse(hashCodeMessage);
    await (database.delete(database.firebaseDrift)
          ..where(
            (tbl) => tbl.id.equals(id),
          ))
        .go();
    await shownotification();
  }

  @override
  Future<void> shownotification() async {
    await initFcmDb();
    while (!initialized) {
      await Future.delayed(const Duration(milliseconds: 100));
    }
    if (error) {
      await LogDatabase.instance.logError(
        'Banco de dados FCM',
        '',
        'Erro ao carregar o banco de dados',
        {},
      );
      return;
    }

    final values = await database.select(database.firebaseDrift).get();
    if (values.isEmpty) {
      return;
    }
    final value = values.first;
    final key = value.id.toString();
    final data = jsonDecode(value.texto) as Map<String, dynamic>;
    final remoteMessage = RemoteMessage.fromMap(data);
    await showFlutterNotificationOpened(remoteMessage, key);
  }
}
