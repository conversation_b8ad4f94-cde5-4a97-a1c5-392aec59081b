<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>NSCameraUsageDescription</key>
	<string>Need Camera Access to Scan QR Code Image Where Customer ID is Saved</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Para otimizar as suas rotas de entregas o app Octalog: para entregadores precisa de permissão da sua localização atual.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Para otimizar as suas rotas de entregas o app Octalog: para entregadores precisa de permissão da sua localização atual.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Para otimizar as suas rotas de entregas o app Octalog: para entregadores precisa de permissão da sua localização atual.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Para conversar com nosso Serviço de Atendimento ao Cliente (SAC) por voz, esta aplicação precisa de permissão para acessar o microfone. Por favor, conceda o acesso.</string>
	<key>NSMotionUsageDescription</key>
	<string>O aplicativo "Octalog: para entregadores" utiliza dados de movimento para otimizar as rotas de entrega e garantir a precisão do serviço de entrega. Isso ajuda a melhorar a eficiência da entrega, oferecendo a melhor rota possível ao motorista.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Read QR codes from saved images</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>Permita que o nosso aplicativo utilize o reconhecimento de fala para interpretar comandos de voz e melhorar a interação do usuário.</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Octalog</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>octalog</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
</dict>
</plist>
