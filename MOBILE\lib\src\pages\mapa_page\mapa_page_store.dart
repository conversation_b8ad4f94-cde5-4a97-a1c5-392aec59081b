import 'package:flutter/cupertino.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:octalog/src/pages/mapa_page/mapa_page_state.dart';

import '../../helpers/gps/gps_contract.dart';

class MapaPageStore {
  ValueNotifier<MapaPageState> state = ValueNotifier(
    MapaPageState(
      mapaPageController: MapController(),
      isTracking: false,
      latLng: LatLng(0, 0),
    ),
  );

  MapaPageStore() {
    _init();
  }
  void setIsTracking(bool value) {
    state.value = state.value.copyWith(isTracking: value);
  }

  void _setLatLng(LatLng value) {
    state.value = state.value.copyWith(latLng: value);
  }

  Future<void> _init() async {
    bool primeiro = true;
    while (true) {
      try {
        final loc = await GpsHelperContract.instance.receberLocalizacao();
        _setLatLng(LatLng(loc.latitude, loc.longitude));
        if (state.value.isTracking || primeiro) {
          state.value.mapaPageController.move(
            LatLng(loc.latitude, loc.longitude),
            state.value.mapaPageController.zoom,
          );
        }
        primeiro = false;
      } catch (_) {}
      await Future.delayed(const Duration(seconds: 4));
    }
  }
}
