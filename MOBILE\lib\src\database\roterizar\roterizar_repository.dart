import 'package:flutter/material.dart';
import 'package:octalog/src/helpers/web_connector.dart';
import 'package:octalog/src/models/roterizar_model.dart';

// api para roterizar o log de transação
class RoterizarRepository {
  Future<void> sendRoterizar(RoterizarModel roterizarModel) async {
    WebConnector connector = WebConnector();
    try {
      final response = await connector.post('/atividades/roterizar',
          body: roterizarModel.toMap());

      debugPrint('Mostrar dados: ${response.data}');
    } catch (e, s) {
      debugPrint('${e}error 5353535');
      debugPrint('$s show me');
    }
  }
}
