import 'package:flutter/material.dart';
import 'package:octalog/src/models_new/endereco_new.dart';

import '../../../database/entrega/entrega_database.dart';
import '../home.dart';

class AtividadeCronometroTile extends StatefulWidget {
  final EnderecoNew atividade;
  const AtividadeCronometroTile({
    super.key,
    required this.atividade,
  });

  @override
  State<AtividadeCronometroTile> createState() =>
      _AtividadeCronometroTileState();
}

class _AtividadeCronometroTileState extends State<AtividadeCronometroTile> {
  DateTime? inicio;
  String time = '00:00';

  Future<void> cronometro() async {
    final box = await EntregaDatabase.instance.getBanco();
    while (true) {
      try {
        inicio = (await box.get('inicio')) as DateTime?;
        if (inicio != null) {
          final now = DateTime.now();
          final diff = now.difference(inicio!);
          final horas = diff.inHours;
          final minutos = diff.inMinutes - horas * 60;
          final segundos = diff.inSeconds - horas * 3600 - minutos * 60;
          setState(() {
            time =
                '${minutos.toString().padLeft(2, '0')}:${segundos.toString().padLeft(2, '0')}';
          });
        }
      } catch (_) {}
      await Future.delayed(const Duration(seconds: 1));
    }
  }

  @override
  void initState() {
    super.initState();
    cronometro();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(
            builder: (context) => const Home(),
          ),
          (route) => false,
        );
      },
      child: Column(
        children: [
          const SizedBox(height: 10),
          Container(
            width: double.infinity,
            color: Colors.orange.withOpacity(.6),
            child: Row(
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(10),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'ENTREGA EM ANDAMENTO',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          widget.atividade.enderecoCompleto,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // Container(
                //   margin: const EdgeInsets.only(right: 10),
                //   child: Container(
                //     height: 55,
                //     width: 55,
                //     decoration: BoxDecoration(
                //       color: Colors.white,
                //       borderRadius: BorderRadius.circular(
                //         30,
                //       ),
                //       border: Border.all(
                //         color: ColorsCustom.customOrange,
                //         width: 2,
                //       ),
                //     ),
                //     child: Center(
                //       child: Text(time),
                //     ),
                //   ),
                // ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
