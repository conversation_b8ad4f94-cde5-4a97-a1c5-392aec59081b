import 'package:flutter/material.dart';
import 'package:map_fields/map_fields.dart';

class Tag {
  final String descricao;
  final String cor;

  Tag({
    required this.descricao,
    required this.cor,
  });

  Map<String, dynamic> toHiveMap() {
    return {
      'descricao': descricao,
      'cor': cor,
    };
  }

  factory Tag.fromHiveMap(Map<String, dynamic> map) {
    final MapFields t = MapFields.load(map);
    return Tag(
      // descricao: map['descricao'] as String,
      // cor: map['cor'] as String,
      descricao: t.getString('descricao', ''),
      cor: t.getString('cor', ''),
    );
  }

  factory Tag.fromJson(Map<String, dynamic> json) {
    final MapFields t = MapFields.load(json);
    return Tag(
      // descricao: (json["Descricao"] ?? '').toString(),
      // cor: (json["Cor"] ?? '').toString(),
      descricao: t.getString('Descricao', ''),
      cor: t.getString('Cor', ''),
    );
  }
  Color getColor() {
    return Color(int.parse('0xFF$cor'));
  }
}
