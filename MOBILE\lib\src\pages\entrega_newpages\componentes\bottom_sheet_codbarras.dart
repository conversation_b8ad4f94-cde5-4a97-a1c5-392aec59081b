import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/models/expedir_model.dart';
import 'package:octalog/src/pages/home/<USER>';
import 'package:octalog/src/pages/home/<USER>';
import 'package:url_launcher/url_launcher.dart';

import '../../../components/buttom_ls/button_ls_custom.dart';
import '../../../database/config_blob/config_database.dart';
import '../../../utils/colors.dart';

class ButtomSheetCodbaras extends StatefulWidget {
  final String barcode;
  final List<ExpedirModel> expedirModel;
  final bool expedir;
  const ButtomSheetCodbaras({
    super.key,
    required this.barcode,
    required this.expedirModel,
    required this.expedir,
  });

  @override
  State<ButtomSheetCodbaras> createState() => _ButtomSheetCodbarasState();
}

class _ButtomSheetCodbarasState extends State<ButtomSheetCodbaras> {
  final controller = HomeController.instance;
  int? indexEscolhido;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: SizedBox(
        height: widget.expedirModel.length > 1
            ? MediaQuery.of(context).size.height * 0.55
            : widget.expedirModel.length > 6
                ? MediaQuery.of(context).size.height * 0.88
                : MediaQuery.of(context).size.height * 0.88,
        width: MediaQuery.of(context).size.width,
        child: Column(
          children: [
            SizedBox(
              height: 30,
              width: double.infinity,
              child: Center(
                child: Text(
                  'Escolha o cliente',
                  style: GoogleFonts.roboto(
                    fontSize: 15,
                    color: ColorsCustom.customOrange,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            const Divider(
              color: ColorsCustom.customOrange,
              thickness: 3,
              height: 3,
            ),
            Expanded(
              child: ListView.builder(
                itemCount: widget.expedirModel.length,
                itemBuilder: (_, index) {
                  final item = widget.expedirModel[index];
                  final escolhido = indexEscolhido == index;
                  return ListTile(
                    onTap: () {
                      setState(() {
                        indexEscolhido = index;
                      });
                    },
                    title: Row(
                      children: [
                        Icon(
                          escolhido
                              ? Icons.radio_button_checked
                              : Icons.radio_button_unchecked,
                          color: escolhido
                              ? ColorsCustom.customGreen
                              : ColorsCustom.customGreyLight,
                          size: escolhido ? 20 : 14,
                        ),
                        const SizedBox(width: 15),
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              item.nome ?? 'Sem nome',
                              style: GoogleFonts.roboto(
                                fontSize: 15,
                                color: ColorsCustom.customBlack,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(
                20,
                30,
                20,
                10,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: ButtonLsCustom(
                      onPressed: () async {
                        Navigator.of(context).pop();
                        final responseConfig =
                            await ConfigDatabase.instance.getConfig();
                        final fone = responseConfig.foneSac;
                        launchUrl(Uri.parse('tel://$fone'));
                      },
                      text: 'SAC',
                      colorBackground: Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    flex: 2,
                    child: ButtonLsCustom(
                      message:
                          indexEscolhido == null ? 'Selecione um item' : null,
                      onPressed: indexEscolhido != null
                          ? () async {
                              if (indexEscolhido != null) {
                                await controller.expedirPedido(
                                  widget.barcode,
                                  widget.expedirModel[indexEscolhido!].iDLocal,
                                  widget.expedir,
                                );
                                Navigator.of(context).pushAndRemoveUntil(
                                    MaterialPageRoute(
                                        builder: (context) => const Home(
                                              enterAtividade: false,
                                            )),
                                    (route) => false);
                              }
                            }
                          : null,
                      text: 'CONFIRMAR',
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
