// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:octalog/src/components/custom_scaffold/custom_scaffold.dart';
import 'package:octalog/src/pages/entrega_newpages/componentes/card_endereco.dart';
import 'package:octalog/src/pages/entregas/components/entrega_card.dart';
import '../../../../components/buttom_ls/button_ls_custom.dart';
import '../../../../helpers/web_connector.dart';
import '../../../../utils/colors.dart';
import '../../../home/<USER>';
import '../../controller/entrega_new_state.dart';
import '../../controller/entrega_new_store.dart';
import '../../ocorrencias/entrega_negativa.dart';

class EntregaInicio extends StatefulWidget {
  final EntregaNewStore store;

  const EntregaInicio({super.key, required this.store});

  @override
  State<EntregaInicio> createState() => _EntregaInicioState();
}

class _EntregaInicioState extends State<EntregaInicio> {
  bool isColor = true;
  bool loadingBotao = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ValueListenableBuilder<EntregaNewState>(
        valueListenable: widget.store.state,
        builder: (BuildContext context, EntregaNewState value, Widget? child) {
          final atividade = value.atividade;

          return CustomScaffold(
            canPop: false,
            isColorIcon: isColor,
            cameraTela: () async {
              final fotoInicio = await WebConnector().tirarFoto(context);
              if (fotoInicio == null) return;
              widget.store.upLoadFotoInicio(fotoInicio, true);
              setState(() {
                isColor = false;
              });
            },
            onPopClose: () async {
              Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const Home(enterAtividade: false)));
              return true;
            },
            onPop: () {
              Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const Home(enterAtividade: false)));
            },
            title:
                atividade.clientes.last.acareacao
                    ? '${atividade.volumesLength} acareação'
                    : '${atividade.volumesLength} ${atividade.volumesLength == 1 ? 'pedido' : 'pedidos'} para entrega',
            child: Column(
              children: [
                CardEndereco(store: widget.store),
                const SizedBox(height: 20),
                Expanded(
                  child: SizedBox(
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: value.restantes.length,
                      itemBuilder: (context, index) {
                        final cliente = value.restantes[index];
                        return Column(
                          children: [
                            EntregaCard(
                              atividade: atividade,
                              etapa: value.etapa,
                              cliente: cliente,
                              onTap: () async {
                                bool infNegativa = true;
                                final exibir = widget.store.filtrarStatusAtividadesChild(indexClienteRestante: value.indexClienteEscolhido);
                                if (exibir!.isEmpty) return;
                                if (isColor) {
                                  infNegativa = await showDialog(
                                    barrierDismissible: false,
                                    context: context,
                                    builder:
                                        (ctx) => AlertDialog(
                                          title: const Text('Atenção'),
                                          content: const Text('Você não tirou foto deseja continuar?'),
                                          actions: [
                                            Row(
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              mainAxisSize: MainAxisSize.max,
                                              children: [
                                                TextButton(
                                                  onPressed: () {
                                                    Navigator.pop(ctx, false);
                                                  },
                                                  child: Text('CANCELAR', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.grey.shade800)),
                                                ),
                                                TextButton(
                                                  onPressed: () {
                                                    Navigator.pop(ctx, true);
                                                  },
                                                  child: const Text(
                                                    'CONFIRMAR',
                                                    style: TextStyle(fontWeight: FontWeight.bold, color: ColorsCustom.customOrange),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                  );
                                }
                                if (infNegativa) {
                                  showModalBottomSheet(
                                    shape: const RoundedRectangleBorder(
                                      borderRadius: BorderRadius.only(topLeft: Radius.circular(30), topRight: Radius.circular(30)),
                                    ),
                                    isScrollControlled: true,
                                    isDismissible: true,
                                    context: context,
                                    builder:
                                        (_) => EntregaNegativa(
                                          store: widget.store,
                                          indexClienteRestante: index,
                                          isOcorrenciaGlobal: false,
                                          removerAcareacao: atividade.clientes.last.acareacao,
                                        ),
                                  );
                                }
                              },
                            ),
                            const SizedBox(height: 5),
                            Visibility(
                              visible: value.restantes.length > 1 && index != value.restantes.length - 1,
                              child: const Padding(padding: EdgeInsets.symmetric(horizontal: 22), child: Divider(thickness: 0.5, color: Colors.grey)),
                            ),
                            const SizedBox(height: 10),
                          ],
                        );
                      },
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                const SizedBox(
                  child: Text(
                    'Importante:\nCliente receberá whatsapp informando o início da entrega.',
                    style: TextStyle(fontSize: 15, fontWeight: FontWeight.bold),
                  ),
                ),
                const SizedBox(height: 5),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 22),
                  child: ButtonLsCustom(
                    text: 'INICIAR ENTREGA',
                    isLoading: loadingBotao,
                    onPressed: () async {
                      setState(() => loadingBotao = true);
                      // await widget.store.timePedidoOld();
                      await widget.store.iniciarDeslocamento();
                      await widget.store.iniciarDeslocamentoOnline();
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
