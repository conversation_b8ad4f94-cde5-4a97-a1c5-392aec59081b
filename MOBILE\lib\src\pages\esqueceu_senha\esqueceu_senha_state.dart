class EsqueceuSenhaState {
  final bool loading;
  final bool success;
  final bool error;

  EsqueceuSenhaState({
    this.loading = false,
    this.success = false,
    this.error = false,
  });

  EsqueceuSenhaState copyWith({
    bool? loading,
    bool? success,
    bool? error,
  }) {
    return EsqueceuSenhaState(
      loading: loading ?? this.loading,
      success: success ?? this.success,
      error: error ?? this.error,
    );
  }
}
