import 'package:asuka/asuka.dart' as asuka;
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../utils/colors.dart';

class ElevatedLsButton extends StatelessWidget {
  final String text;
  final Function()? onPressed;
  final bool isLoading;
  final String? message;

  const ElevatedLsButton(
      {super.key,
      required this.text,
      this.onPressed,
      required this.isLoading,
      this.message});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 56,
      child: ElevatedButton(
        style: ButtonStyle(
          // backgroundColor: WidgetStateProperty.all<Color>(
          //   ColorsCustom.customOrange,
          // ),
          backgroundColor:
              WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return Colors.grey;
            }
            return ColorsCustom.customOrange;
          }),
          // shape: WidgetStateProperty.all<OutlinedBorder>(
          //   RoundedRectangleBorder(
          //     borderRadius: BorderRadius.circular(8),
          //   ),
          // ),
          shape: WidgetStateProperty.all<OutlinedBorder>(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
        onPressed: funcIfDisabled(
          context,
          message,
          isLoading,
          onPressed,
        ),
        child: SizedBox(
          height: 45,
          width: MediaQuery.of(context).size.width * 0.75,
          child: Center(
            child: Text(
              text,
              style: GoogleFonts.roboto(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: ColorsCustom.customWhite,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

Function()? funcIfDisabled(
    context, String? message, bool isLoading, Function()? onPressed) {
  if (isLoading) {
    return null;
  }
  if (message != null) {
    return () {
      // ignore: deprecated_member_use
      asuka.Asuka.showDialog(
        barrierColor: Colors.black.withOpacity(0.8),
        builder: (context) => AlertDialog(
          title: const Text('Atenção'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text(
                'CONFIRMAR',
                style: TextStyle(color: ColorsCustom.customOrange),
              ),
            )
          ],
        ),
      );
    };
  }
  return onPressed;
}
