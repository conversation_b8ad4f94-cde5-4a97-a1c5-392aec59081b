import 'package:octalog/src/models/volume.dart';
import 'package:map_fields/map_fields.dart';

import 'id_status_atividade_enum.dart';
import 'tag.dart';

class Pedido {
  final String logo;
  final String loja;
  final String rastreio;
  final List<Volume> volumes;
  final List<Tag> tags;
  Pedido({
    required this.logo,
    required this.loja,
    required this.rastreio,
    required this.volumes,
    required this.tags,
  });

  Pedido copyWith({
    String? logo,
    String? loja,
    String? rastreio,
    List<Volume>? volumes,
    List<Tag>? tags,
  }) {
    return Pedido(
      logo: logo ?? this.logo,
      loja: loja ?? this.loja,
      rastreio: rastreio ?? this.rastreio,
      volumes: volumes ?? this.volumes,
      tags: tags ?? this.tags,
    );
  }

  Pedido get apenasEntrega {
    return copyWith(
      volumes: volumes
          .where((e) => e.idStatusAtividadeEnum?.isEntrega ?? false)
          .toList(),
    );
  }

  Pedido whereStatus(IdStatusAtividadeEnum value) {
    return copyWith(
      volumes: volumes.where((e) => e.idStatusAtividadeEnum == value).toList(),
    );
  }

  Pedido withStatus(int value, String newSituacao) {
    return copyWith(
      volumes: volumes
          .map((e) => e.copyWith(
                idStatusAtividade: value,
                statusAtividade: newSituacao,
              ))
          .toList(),
    );
  }

  Map<String, dynamic> toHiveMap() {
    return {
      'logo': logo,
      'loja': loja,
      'rastreio': rastreio,
      'volumes': volumes.map((e) => e.toHiveMap()).toList(),
      'tags': tags.map((e) => e.toHiveMap()).toList(),
    };
  }

  factory Pedido.fromHiveMap(Map<String, dynamic> map) {
    final MapFields p = MapFields.load(map);
    return Pedido(
      // logo: map['logo'] as String,
      // loja: map['loja'] as String,
      // rastreio: map['rastreio'] as String,
      // volumes: (map['volumes'] as List<dynamic>)
      // .map((e) => Volume.fromHiveMap(e))
      // .toList(),
      // tags: (map['tags'] as List<dynamic>)
      // .map((e) => Tag.fromHiveMap(e))
      // .toList(),
      logo: p.getString('logo', ''),
      loja: p.getString('loja', ''),
      rastreio: p.getString('rastreio', ''),
      volumes: p
          .getList<Map<String, dynamic>>('volumes')
          .map<Volume>(
              (Map<String, dynamic> volume) => Volume.fromHiveMap(volume))
          .toList(),
      tags: p
          .getList<Map<String, dynamic>>('tags')
          .map<Tag>((Map<String, dynamic> tag) => Tag.fromHiveMap(tag))
          .toList(),
    );
  }

  factory Pedido.fromJson(Map<String, dynamic> map) {
    final MapFields p = MapFields.load(map);
    return Pedido(
      // logo: (map['Logo'] ?? '') as String,
      // loja: (map['Loja'] ?? '') as String,
      // rastreio: (map['Rastreio'] ?? '') as String,
      // volumes: ((map['Volumes'] ?? []).cast<Map<String, dynamic>>())
      //     .map<Volume>((Map<String, dynamic> volume) => Volume.fromJson(volume))
      //     .toList(),
      // tags: ((map['Tags'] ?? []).cast<Map<String, dynamic>>())
      //     .map<Tag>((Map<String, dynamic> tag) => Tag.fromJson(tag))
      //     .toList(),
      logo: p.getString('Logo', ''),
      loja: p.getString('Loja', ''),
      rastreio: p.getString('Rastreio', ''),
      volumes: p
          .getList<Map<String, dynamic>>('Volumes')
          .map<Volume>((Map<String, dynamic> volume) => Volume.fromJson(volume))
          .toList(),
      tags: p
          .getList<Map<String, dynamic>>('Tags')
          .map<Tag>((Map<String, dynamic> tag) => Tag.fromJson(tag))
          .toList(),
    );
  }

  List<IdStatusAtividadeEnum> get status {
    return volumes
        .map((e) => e.idStatusAtividadeEnum)
        .where((e) => e != null)
        .toList()
        .cast<IdStatusAtividadeEnum>();
  }

  List<String> get statusString => volumes.map((e) => e.statustitle).toList();
  // List<String> get statusString => status.map((e) => e.title).toList();

  int get volumesLength => volumes.length;

  bool find(String search) {
    return loja.toLowerCase().trim().contains(search.toLowerCase().trim()) ||
        rastreio.toLowerCase().trim().contains(search.toLowerCase().trim()) ||
        volumes.where((volume) => volume.find(search)).isNotEmpty;
  }

  List<int> get idosList {
    return volumes.map((e) => e.idos).toList();
  }

  bool get receberValor {
    return volumes.map((e) => e.receberValor).contains(true);
  }
}
