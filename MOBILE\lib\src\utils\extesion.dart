extension StringExt on String {
  String substringLs(int i, int f) {
    if (length < f) {
      return this;
    }
    return substring(i, f);
  }

  String get iniciais {
    if (trim().isEmpty) {
      return 'SN';
    }

    final nomes = trim().split(' ').map((e) => e.trim()).toList();
    nomes.removeWhere((e) => e.isEmpty);
    if (nomes.length == 1) {
      return nomes[0].substring(0, 1).toUpperCase();
    }
    return '${nomes[0].substring(0, 1).toUpperCase()}${nomes[1].substring(0, 1).toUpperCase()}';
  }
}

extension IntExt on int {
  String padLeft(int i) {
    return toString().padLeft(i, '0');
  }
}

extension DateTimeExt on DateTime {
  String get dataEvento {
    return '${year.padLeft(4)}-${month.padLeft(2)}-${day.padLeft(2)} ${hour.padLeft(2)}:${minute.padLeft(2)}:${second.padLeft(2)}';
  }

  String get dataFormatada {
    return '${hour.padLeft(2)}:${minute.padLeft(2)}';
  }

  String get dataPtBr {
    return '${day.padLeft(2)}/${month.padLeft(2)}/${year.padLeft(4)}';
  }

  String get horaPtBr {
    return '${hour.padLeft(2)}:${minute.padLeft(2)}:${second.padLeft(2)}';
  }

  String get dataHoraPtBr {
    return '${day.padLeft(2)}/${month.padLeft(2)}/${year.padLeft(4)} ${hour.padLeft(2)}:${minute.padLeft(2)}';
  }

  String get horaPtBrSemSegund {
    return '${hour.padLeft(2)}:${minute.padLeft(2)}';
  }

  DateTime get dataHoraServidorFomart {
    final data = toUtc();
    final dataUtc = data.subtract(const Duration(hours: 3));

    return dataUtc;
  }

  DateTime get dataHoraServidor {
    final data = toUtc();
    final dataUtc = data.subtract(const Duration(hours: 3));
    return DateTime(dataUtc.year, dataUtc.month, dataUtc.day, dataUtc.hour,
        dataUtc.minute, dataUtc.second);
  }

  String get dataPtBrDiaMes {
    final dia = day.padLeft(2);
    final mes = month.padLeft(2);
    final mesExtenso = {
      '01': 'jan',
      '02': 'fev',
      '03': 'mar',
      '04': 'abr',
      '05': 'mai',
      '06': 'jun',
      '07': 'jul',
      '08': 'ago',
      '09': 'set',
      '10': 'out',
      '11': 'nov',
      '12': 'dez',
    };
    final mesExtensoStr = mesExtenso[mes];
    return '$dia $mesExtensoStr.';
  }

  static String exibirTempoDecorridoAPartirDoTimestamp(DateTime timestamp) {
    final int ano = timestamp.year;
    final int mes = timestamp.month;
    final int dia = timestamp.day;
    final int hora = timestamp.hour;
    final int minuto = timestamp.minute;

    final DateTime dataDoVideo = DateTime(ano, mes, dia, hora, minuto);
    final int diferencaEmHoras = DateTime.now().difference(dataDoVideo).inHours;

    String tempoDecorrido = '';
    String unidadeDeTempo = '';
    int valorDeTempo = 0;

    if (diferencaEmHoras < 1) {
      final diferencaEmMinutos =
          DateTime.now().difference(dataDoVideo).inMinutes;
      valorDeTempo = diferencaEmMinutos;

      unidadeDeTempo = 'min';
    } else if (diferencaEmHoras < 24) {
      valorDeTempo = diferencaEmHoras;
      unidadeDeTempo = 'hr';
    } else if (diferencaEmHoras >= 24 && diferencaEmHoras < 24 * 7) {
      valorDeTempo = (diferencaEmHoras / 24).floor();
      unidadeDeTempo = 'dia';
    } else if (diferencaEmHoras >= 24 * 7 && diferencaEmHoras < 24 * 30) {
      valorDeTempo = (diferencaEmHoras / (24 * 7)).floor();
      unidadeDeTempo = 'semana';
    } else if (diferencaEmHoras >= 24 * 30 && diferencaEmHoras < 24 * 12 * 30) {
      valorDeTempo = (diferencaEmHoras / (24 * 30)).floor();
      unidadeDeTempo = 'mês';
    } else {
      valorDeTempo = (diferencaEmHoras / (24 * 365)).floor();
      unidadeDeTempo = 'ano';
    }

    if (valorDeTempo == 0) {
      return 'agora';
    }

    tempoDecorrido = '$valorDeTempo $unidadeDeTempo';
    tempoDecorrido += valorDeTempo > 1 ? '' : '';

    return tempoDecorrido;
  }
}
