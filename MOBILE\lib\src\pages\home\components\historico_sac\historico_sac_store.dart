import 'package:flutter/material.dart';
import 'package:octalog/src/helpers/web_connector.dart';

import '../../../../models/historico_sac.dart';
import 'historico_sac_state.dart';

class HistoricoSacStore {
  final ValueNotifier<HistoricoSacState> state =
      ValueNotifier<HistoricoSacState>(HistoricoSacState.initial());

  void setHistoricoSac(List<HistoricoSacModel> historicoSac) {
    state.value = state.value.setHistoricoSac(historicoSac);
  }

  void setLoading(bool loading) {
    state.value = state.value.setLoading(loading);
  }

  void setDataInicialDataFinal(DateTime dataInicial, DateTime dataFinal) {
    state.value = state.value.setDataInicial(dataInicial);
    state.value = state.value.setDataFinal(dataFinal);
  }

  void setErrorMessage(String errorMessage) {
    state.value = state.value.setErrorMessage(errorMessage);
  }

  void dispose() {
    state.dispose();
  }

  void reset() {
    state.value = HistoricoSacState.initial();
  }

  void resetError() {
    state.value = state.value.setErrorMessage('');
  }

  void resetHistoricoSac() {
    state.value = state.value.setHistoricoSac([]);
  }

  Future buscarPedidos() async {
    final conn = WebConnector();
    final response = await conn.get('/sac/historicoSac', queryParameters: {
      'dataInicial': state.value.dataInicial,
      'dataFinal': state.value.dataFinal,
    });

    final historicoSac = HistoricoSacModel.fromJsonList(response.data);
    setHistoricoSac(historicoSac);
  }
}
