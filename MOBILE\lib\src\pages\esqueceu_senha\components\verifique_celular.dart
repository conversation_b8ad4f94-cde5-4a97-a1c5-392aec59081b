import 'package:flutter/material.dart';
import 'package:octalog/src/components/buttom_ls/button_ls_custom.dart';
import 'package:octalog/src/components/custom_scaffold/custom_scaffold.dart';
import 'package:octalog/src/components/title_widget/title_widget.dart';

import '../../login/login_page.dart';

class VerifiqueCelular extends StatelessWidget {
  const VerifiqueCelular({super.key});

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      onPop: () => Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(
          builder: (context) => const LoginPage(),
        ),
        (route) => false,
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 36),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const TitleWidget(
                title: 'Verifique se recebeu\num SMS no seu celular',
                subtitle: '',
              ),
              const SizedBox(height: 60),
              ButtonLsCustom(
                text: 'LOGIN',
                onPressed: () => Navigator.of(context).pushAndRemoveUntil(
                  MaterialPageRoute(
                    builder: (context) => const LoginPage(),
                  ),
                  (route) => false,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
