["C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/sac/acabou-tempo-espera.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/sac/alert.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/sac/atendete.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/sac/horario-nao-atendido.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/sac/sem-atendentes-disponiveis.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/sac/sucesso.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/animations/loading.riv", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/notification/music.mp3", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/flutter_map/lib/assets/flutter_map_logo.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/font_awesome_flutter/lib/fonts/fa-brands-400.ttf", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/font_awesome_flutter/lib/fonts/fa-regular-400.ttf", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/font_awesome_flutter/lib/fonts/fa-solid-900.ttf", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/iconsax/lib/assets/fonts/iconsax.ttf", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders/ink_sparkle.frag", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json"]