import 'package:flutter/material.dart';
import 'package:octalog/src/models_new/mensagens_new.dart';

class MensagemWidget extends StatelessWidget {
  final double scale;
  final List<MensagensNew> mensagens;
  const MensagemWidget({
    super.key,
    this.scale = 1,
    required this.mensagens,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: mensagens.map((e) {
          final title = e.descricao;
          return Container(
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            padding: const EdgeInsets.only(
              left: 10,
              right: 35,
              top: 5,
              bottom: 5,
            ),
            width: MediaQuery.of(context).size.width,
            child: Padding(
              padding: const EdgeInsets.only(right: 50),
              child: RichText(
                overflow: TextOverflow.visible,
                textAlign: TextAlign.left,
                text: TextSpan(
                    style: const TextStyle(color: Colors.black, fontSize: 14),
                    text: title),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}
