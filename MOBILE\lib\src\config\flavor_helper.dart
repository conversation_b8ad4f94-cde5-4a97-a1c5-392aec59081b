import 'package:flutter/foundation.dart';
import 'flavor_config.dart';

class FlavorHelper {
  static FlavorType getCurrentFlavor() {
    // Em debug mode, podemos usar uma variável de ambiente ou constante
    if (kDebugMode) {
      const String? flavorName = String.fromEnvironment('FLAVOR');
      if (flavorName != null) {
        return _parseFlavorFromString(flavorName);
      }
    }

    // Para release builds, tentamos detectar pelo package name ou outras formas
    return _detectFlavorFromBuild();
  }

  static FlavorType _parseFlavorFromString(String flavorName) {
    switch (flavorName.toLowerCase()) {
      case 'arcargo':
        return FlavorType.arcargo;
      case 'connect':
        return FlavorType.connect;
      case 'rondolog':
        return FlavorType.rondolog;
      case 'octalog':
      default:
        return FlavorType.octalog;
    }
  }

  static FlavorType _detectFlavorFromBuild() {
    // Para Android, podemos usar BuildConfig se disponível
    // Para iOS, podemos usar Info.plist values
    // Por enquanto, retornamos o padrão
    return FlavorType.octalog;
  }

  static void initializeFlavor() {
    final flavor = getCurrentFlavor();
    FlavorConfig.initialize(flavor);
  }
}
