# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\src\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\projetos\\octa.log\\MOBILE" PROJECT_DIR)

set(FLUTTER_VERSION "1.2.2+23" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 2 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 2 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 23 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\src\\flutter"
  "PROJECT_DIR=C:\\projetos\\octa.log\\MOBILE"
  "FLUTTER_ROOT=C:\\src\\flutter"
  "FLUTTER_EPHEMERAL_DIR=C:\\projetos\\octa.log\\MOBILE\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\projetos\\octa.log\\MOBILE"
  "FLUTTER_TARGET=C:\\projetos\\octa.log\\MOBILE\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\projetos\\octa.log\\MOBILE\\.dart_tool\\package_config.json"
)
