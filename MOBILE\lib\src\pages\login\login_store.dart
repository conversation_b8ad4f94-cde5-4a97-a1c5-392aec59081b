import 'package:flutter/cupertino.dart';
import 'package:octalog/src/models/user.dart';
import 'package:octalog/src/pages/login/login_state.dart';

import '../../helpers/login/login.dart';
import '../../utils/offline_helper/offline_helper_store.dart';

class LoginStore {
  ValueNotifier<LoginState> state = ValueNotifier(LoginState());
  final OfflineStore offlineStore = OfflineStore();

  void setLoading(bool value) {
    state.value = state.value.copyWith(loading: value);
  }

  void setErro(bool value) {
    state.value = state.value.copyWith(error: value);
  }

  void setSuccess(bool value) {
    state.value = state.value.copyWith(success: value);
  }

  void setMessage(String value) {
    state.value = state.value.copyWith(message: value);
  }

  Future<UserData?> login(id, senha) async {
    setLoading(true);
    setErro(false);
    setSuccess(false);
    try {
      final response = id == '3329'
          ? senha.toString().trim().toUpperCase() == 'AL'
              ? UserData(
                  nomeCompleto: 'Agente de Teste Das Lojas',
                  telefone: '32999999999',
                  email: '<EMAIL>',
                  uf: 'SP',
                  foto: '',
                  cpf: '34448306020',
                  idTipoAgenteUberizado: 0,
                  userLogin: UserLogin(
                    usuario: id,
                    senha: senha,
                    token: "****************************",
                    atualizarCadastro: false,
                  ),
                  permiteTransferenciaMobile: false,
                  loginMocked: true,
                )
              : null
          : await Login.instance.login(id, senha).timeout(
              const Duration(seconds: 30),
              onTimeout: () {
                setMessage(
                    'Tempo de conexão excedido \n Verifique sua conexão com a internet e tente novamente');
                setErro(true);
                setLoading(false);
                return null;
              },
            );
      setLoading(false);
      if (response == null) {
        if (!state.value.error) {
          setMessage(Login.instance.mensagem);

          if (Login.instance.usuarioInativo) {
            setErro(true);
          } else {
            setErro(true);
          }
          return null;
        }
      } else {
        setSuccess(true);
        return response;
      }
    } catch (e) {
      setErro(true);
      setMessage(e.toString());
      setLoading(false);
      return null;
    }
    return null;
  }

  void setVisiblePassword(bool value) {
    state.value = state.value.copyWith(visiblePassword: value);
  }
}
