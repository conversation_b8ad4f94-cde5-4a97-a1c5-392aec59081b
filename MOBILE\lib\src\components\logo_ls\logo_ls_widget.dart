import 'package:flutter/material.dart';
import '../flavor_image/flavor_image.dart';

class LogoLSWidget extends StatelessWidget {
  final double width;
  final double height;
  const LogoLSWidget({
    super.key,
    this.width = 200,
    this.height = 200,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height,
      child: FlavorLogoWhite(
        width: width,
        height: height,
      ),
    );
  }
}
