import 'package:octalog/src/pages/sac_page/enum/sap_page_etapa.dart';
import 'package:octalog/src/pages/sac_page/model/messagem_chat.dart';
import 'package:signalr_core/signalr_core.dart';

import 'model/sac_model_fila.dart';

class SacPageState {
  final bool isLoading;
  final SacPageEtapa etapa;
  final String infoInicialAgente;
  final int idStatusAtividade;
  final int idos;
  final SacModelFila? sacModelFila;
  final String? mensagem;
  final bool isBotaoMensagem;
  final bool isLoadingSacEmAberto;
  final bool isBotaoCancelarSAC;
  final bool sacCancelado;
  final HubConnection? socket;
  final List<MessagemChat>? messagesChat;
  final bool? indicadorDeBuscaFila;
  final bool agenteDesejaAguardarSAC;
  final bool exibirBotaoEsperarSAC;
  final bool isLoadingLocalizacao;
  SacPageState({
    required this.isLoading,
    required this.etapa,
    required this.infoInicialAgente,
    required this.idStatusAtividade,
    required this.idos,
    required this.isLoadingSacEmAberto,
    this.sacModelFila,
    this.mensagem,
    required this.isBotaoMensagem,
    required this.isBotaoCancelarSAC,
    required this.sacCancelado,
    this.indicadorDeBuscaFila,
    this.socket,
    this.messagesChat,
    required this.agenteDesejaAguardarSAC,
    required this.exibirBotaoEsperarSAC,
    required this.isLoadingLocalizacao,
  });

  factory SacPageState.init() {
    return SacPageState(
      isLoading: false,
      etapa: SacPageEtapa.mensagem,
      infoInicialAgente: '',
      idStatusAtividade: 0,
      idos: 0,
      sacModelFila: null,
      isBotaoMensagem: false,
      isLoadingSacEmAberto: false,
      isBotaoCancelarSAC: false,
      sacCancelado: false,
      indicadorDeBuscaFila: false,
      agenteDesejaAguardarSAC: true,
      exibirBotaoEsperarSAC: false,
      isLoadingLocalizacao: false,
    );
  }

  SacPageState _copyWith({
    bool? isLoading,
    SacPageEtapa? etapa,
    String? mensagem,
    String? infoInicialAgente,
    int? idStatusAtividade,
    int? idos,
    SacModelFila? sacModelFila,
    bool? isBotaoMensagem,
    bool? isBotaoCancelarSAC,
    bool? isLoadingSacEmAberto,
    bool? sacCancelado,
    bool? indicadorDeBuscaFila,
    HubConnection? socket,
    List<MessagemChat>? messagesChat,
    bool? agenteDesejaAguardarSAC,
    bool? exibirBotaoEsperarSAC,
    bool? isLoadingLocalizacao,
  }) {
    return SacPageState(
      isLoading: isLoading ?? this.isLoading,
      etapa: etapa ?? this.etapa,
      mensagem: mensagem ?? this.mensagem,
      infoInicialAgente: infoInicialAgente ?? this.infoInicialAgente,
      idStatusAtividade: idStatusAtividade ?? this.idStatusAtividade,
      idos: idos ?? this.idos,
      sacModelFila: sacModelFila ?? this.sacModelFila,
      isBotaoMensagem: isBotaoMensagem ?? this.isBotaoMensagem,
      isLoadingSacEmAberto: isLoadingSacEmAberto ?? this.isLoadingSacEmAberto,
      isBotaoCancelarSAC: isBotaoCancelarSAC ?? this.isBotaoCancelarSAC,
      sacCancelado: sacCancelado ?? this.sacCancelado,
      indicadorDeBuscaFila: indicadorDeBuscaFila ?? this.indicadorDeBuscaFila,
      socket: socket ?? this.socket,
      messagesChat: messagesChat ?? this.messagesChat,
      agenteDesejaAguardarSAC:
          agenteDesejaAguardarSAC ?? this.agenteDesejaAguardarSAC,
      exibirBotaoEsperarSAC:
          exibirBotaoEsperarSAC ?? this.exibirBotaoEsperarSAC,
      isLoadingLocalizacao: isLoadingLocalizacao ?? this.isLoadingLocalizacao,
    );
  }

  setLoading(bool isLoading) => _copyWith(isLoading: isLoading);
  setEtapa(SacPageEtapa etapa) => _copyWith(etapa: etapa);
  setMensagem(String mensagem) => _copyWith(mensagem: mensagem);
  setInfoInicialAgente(String infoInicialAgente) =>
      _copyWith(infoInicialAgente: infoInicialAgente);
  setIDStatusAtividadeIDos(int idStatusAtividade, int idos) =>
      _copyWith(idStatusAtividade: idStatusAtividade, idos: idos);
  setSacModelFila(SacModelFila sacModelFila) =>
      _copyWith(sacModelFila: sacModelFila);
  setBotaoMensagem(bool isBotaoMensagem) =>
      _copyWith(isBotaoMensagem: isBotaoMensagem);
  setLoadingSacEmAberto(bool isLoadingSacEmAberto) =>
      _copyWith(isLoadingSacEmAberto: isLoadingSacEmAberto);
  setBotaoCancelarSAC(bool isBotaoCancelarSAC) =>
      _copyWith(isBotaoCancelarSAC: isBotaoCancelarSAC);
  setSacCancelado(bool sacCancelado) => _copyWith(sacCancelado: sacCancelado);
  setIndicadorDeBuscaFila(bool? indicadorDeBuscaFila) =>
      _copyWith(indicadorDeBuscaFila: indicadorDeBuscaFila);
  setSocket(HubConnection socket) => _copyWith(socket: socket);
  setMessagesChat(MessagemChat messagemChat) {
    final List<MessagemChat> messagesChat = this.messagesChat ?? [];
    messagesChat.add(messagemChat);
    return _copyWith(messagesChat: messagesChat);
  }

  setAgenteDesejaAguardarSAC(bool agenteDesejaAguardarSAC) =>
      _copyWith(agenteDesejaAguardarSAC: agenteDesejaAguardarSAC);
  setExibirBotaoEsperarSAC(bool exibirBotaoEsperarSAC) =>
      _copyWith(exibirBotaoEsperarSAC: exibirBotaoEsperarSAC);

  SacPageState setLoadingLocalizacao(bool value) {
    return _copyWith(
      isLoadingLocalizacao: value,
    );
  }
}
