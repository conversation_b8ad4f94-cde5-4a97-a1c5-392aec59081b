import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../components/image_perfil/image_perfil.dart';
import '../../../components/reentrega_widget/reentrega_widget.dart';
import '../../../models_new/cliente_new.dart';
import '../../../utils/colors.dart';
import '../../home/<USER>/widget_acareacao/widget_acareacao.dart';

class VolumeCard extends StatelessWidget {
  // final Pedido pedido;
  // final VolumeNew volume;
  final ClienteNew cliente;
  final Function? onTap;
  final bool isFinalizar;
  final Function(String, int)? onReport;
  const VolumeCard({
    super.key,
    // required this.pedido,
    // required this.volume,
    required this.cliente,
    this.onTap,
    this.isFinalizar = false,
    this.onReport,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (onTap != null) {
          onTap!();
        }
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 5),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(color: Colors.grey[300], borderRadius: BorderRadius.circular(50)),
                  child: ImagePerfil(url: cliente.logo ?? '', iniciais: cliente.iniciais, fontSize: 25),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (cliente.complemento.isNotEmpty) const SizedBox(height: 3),
                      if (cliente.complemento.isNotEmpty)
                        Text(cliente.complemento, style: GoogleFonts.roboto(fontSize: 16, fontWeight: FontWeight.bold, color: ColorsCustom.customOrange)),
                      const SizedBox(height: 10),
                      Column(
                        children: List.generate(cliente.volumes.length, (index) {
                          final codRastreio = cliente.volumes[index];
                          final text = Text(
                            codRastreio.os,
                            style: GoogleFonts.roboto(fontSize: 16, fontWeight: FontWeight.bold, color: ColorsCustom.customBlack),
                          );
                          if (cliente.volumes.length > 1 && isFinalizar) {
                            return GestureDetector(
                              behavior: HitTestBehavior.opaque,
                              onTap: () {
                                if (onReport != null) {
                                  onReport!(codRastreio.os, codRastreio.idos);
                                }
                              },
                              child: Padding(
                                padding: const EdgeInsets.symmetric(vertical: 5),
                                child: Row(
                                  children: [const Icon(Icons.warning_amber_rounded, color: ColorsCustom.customOrange), const SizedBox(width: 5), text],
                                ),
                              ),
                            );
                          }
                          return text;
                        }),
                      ),
                      const SizedBox(height: 5),
                      Text(
                        "Transportadora: ${cliente.transportadora}",
                        style: GoogleFonts.roboto(fontSize: 16, fontWeight: FontWeight.bold, color: ColorsCustom.customOrange),
                      ),

                      Padding(
                        padding: const EdgeInsets.only(left: 5),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Text(
                            //   '${cliente.pedidosLength} Pedido${cliente.pedidosLength > 1 ? 's' : ''}',
                            //   style: GoogleFonts.roboto(
                            //     fontSize: 18,
                            //     fontWeight: FontWeight.normal,
                            //     color: ColorsCustom.customGrey,
                            //   ),
                            // ),
                            if (cliente.pontoReferencia.isNotEmpty) const SizedBox(height: 3),
                            if (cliente.pontoReferencia.isNotEmpty)
                              Text(
                                'Ponto de referência: ${cliente.pontoReferencia}',
                                style: GoogleFonts.roboto(fontSize: 18, fontWeight: FontWeight.normal, color: ColorsCustom.customGrey),
                              ),
                            const SizedBox(height: 5),
                            // Text(
                            //   cliente.statusString.join(', '),
                            //   style: GoogleFonts.roboto(
                            //     fontSize: 16,
                            //     fontWeight: FontWeight.normal,
                            //     color: ColorsCustom.customOrange,
                            //   ),
                            // ),
                          ],
                        ),
                      ),
                      ReentregaWidget(scale: 0.8, tags: cliente.tags),
                      Visibility(
                        visible: cliente.acareacao,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 5),
                          child: GestureDetector(
                            onTap: () async {
                              Navigator.push(context, MaterialPageRoute(builder: (context) => AcareacaoWidget(cliente: cliente)));
                              // return asuka.Asuka.showDialog(
                              //   builder: (ctx) => AcareacaoWidget(
                              //     cliente: cliente,
                              //   ),
                              // );
                            },
                            child: const Text(
                              'Informações da acareação',
                              style: TextStyle(color: ColorsCustom.customOrange, fontSize: 15, fontWeight: FontWeight.bold),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
