import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/database/config_blob/config_blob_model.dart';
import 'package:octalog/src/helpers/login/login.dart';
import 'package:octalog/src/pages/cadastro/data/hive_contrato.dart';
import 'package:octalog/src/pages/entrega_newpages/componentes/elevated_ls_button.dart';
import 'package:octalog/src/pages/home/<USER>/aviso_scronimos_pendente.dart';
import 'package:octalog/src/pages/login/login_page.dart';

import '../../../database/config_blob/config_database.dart';
import '../../../helpers/login/login_hive.dart';
import '../../../utils/colors.dart';

class AvisoDeGpsFake extends StatefulWidget {
  final List<AppsFakeGps>? appsFakeGps;
  const AvisoDeGpsFake({super.key, this.appsFakeGps});

  @override
  State<AvisoDeGpsFake> createState() => _AvisoDeGpsFakeState();
}

class _AvisoDeGpsFakeState extends State<AvisoDeGpsFake> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        title: Text(
          'IDENTIFICAÇÃO DE GPS',
          style: GoogleFonts.roboto(
            fontWeight: FontWeight.w800,
            fontSize: 16,
          ),
        ),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.only(
          left: 20,
          right: 20,
          top: 20,
        ),
        child: Scrollbar(
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset('assets/images/fake_gps.png'),
                const Padding(
                  padding: EdgeInsets.all(10.0),
                  child: LinearProgressIndicator(
                    value: 0.3,
                    backgroundColor: Colors.grey,
                    valueColor: AlwaysStoppedAnimation<Color>(
                        ColorsCustom.customOrange),
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Image.asset('assets/images/problem.png', width: 80),
                    const SizedBox(width: 10),
                    Expanded(
                      child: Text(
                        'Algum aplicativo no seu aparelho esta dificultando receber as coordenadas corretas do GPS. Remova ou desabilide o aplicativo.',
                        style: GoogleFonts.roboto(
                          fontWeight: FontWeight.w700,
                          fontSize: 16,
                          color: ColorsCustom.customOrange,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Visibility(
                  visible: widget.appsFakeGps != null,
                  child: Column(
                    children: [
                      Row(
                        children: [
                          const SizedBox(
                            width: 10,
                          ),
                          Image.asset(
                            'assets/images/locations.png',
                            width: 50,
                            color: ColorsCustom.customOrange,
                          ),
                          const SizedBox(width: 10),
                          Text(
                            'POSSÍVEIS APLICATIVOS',
                            style: GoogleFonts.roboto(
                              fontWeight: FontWeight.w800,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: widget.appsFakeGps!.length,
                        itemBuilder: (context, index) {
                          return ListTile(
                            title: Row(
                              children: [
                                const SizedBox(
                                  width: 48,
                                ),
                                const Icon(
                                  Icons.circle_rounded,
                                  color: ColorsCustom.customOrange,
                                  size: 10,
                                ),
                                const SizedBox(
                                  width: 10,
                                ),
                                Expanded(
                                  child: Text(
                                    widget.appsFakeGps![index].nome,
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 3,
                                    style: GoogleFonts.roboto(
                                      fontWeight: FontWeight.w500,
                                      fontSize: 16,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
                SizedBox(height: MediaQuery.of(context).size.height * 0.1),
              ],
            ),
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: ElevatedLsButton(
        text: 'FECHAR O APLICATIVO DA LS',
        onPressed: () async {
          bool continuar = await entrarTelaSicronismo(minimo: 0);
          if (!continuar) return;
          try {
            await LoginHive.instance.clear();
            await Login.instance.logout();

            await ContratoPrefs.instance.clean();

            ConfigDatabase.instance.clear();

            Navigator.pushAndRemoveUntil(
                context,
                MaterialPageRoute(builder: (context) => const LoginPage()),
                (route) => false);
          } catch (_) {}
        },
        isLoading: false,
      ),
    );
  }
}
