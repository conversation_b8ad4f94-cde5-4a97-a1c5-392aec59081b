import 'package:octalog/src/pages/sac_page/enum/message_type.dart';
import 'package:map_fields/map_fields.dart';

class MessagemChat {
  final int idSacAtendimento;
  final String usuario;
  final String conteudo;
  final String iDmensagem;
  final MessagemTipo? origem;
  final DateTime? dateInclusao;
  final String? foto;
  final String? token;
  MessagemChat({
    required this.idSacAtendimento,
    required this.usuario,
    required this.conteudo,
    required this.iDmensagem,
    this.origem,
    this.dateInclusao,
    this.foto,
    this.token,
  });

  factory MessagemChat.fromJson(Map<String, dynamic> json) {
    final MapFields mapFields = MapFields.load(json);
    return MessagemChat(
      idSacAtendimento: mapFields.getInt('idSacAtendimento', 0),
      usuario: mapFields.getString('usuario', ''),
      conteudo: mapFields.getString('conteudo', ''),
      iDmensagem: mapFields.getString('IDMensagem', '0'),
      origem:
          MessagemTipoExtension.fromString(mapFields.getString('origem', '')),
      dateInclusao: DateTime.now(),
      foto: mapFields.getStringNullable('foto'),
      token: mapFields.getStringNullable('token'),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'idSacAtendimento': idSacAtendimento.toString(),
      'Usuario': usuario,
      'IDmensagem': iDmensagem,
      'Conteudo': conteudo,
      'origem': origem?.name,
      'token': token,
    };
  }
}
