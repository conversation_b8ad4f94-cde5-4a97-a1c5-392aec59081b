// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'firebase_drift.dart';

// ignore_for_file: type=lint
class $FirebaseDriftTable extends FirebaseDrift
    with TableInfo<$FirebaseDriftTable, FirebaseDriftData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $FirebaseDriftTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _textoMeta = const VerificationMeta('texto');
  @override
  late final GeneratedColumn<String> texto = GeneratedColumn<String>(
      'texto', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns => [id, texto];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'firebase_drift';
  @override
  VerificationContext validateIntegrity(Insertable<FirebaseDriftData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('texto')) {
      context.handle(
          _textoMeta, texto.isAcceptableOrUnknown(data['texto']!, _textoMeta));
    } else if (isInserting) {
      context.missing(_textoMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  FirebaseDriftData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return FirebaseDriftData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      texto: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}texto'])!,
    );
  }

  @override
  $FirebaseDriftTable createAlias(String alias) {
    return $FirebaseDriftTable(attachedDatabase, alias);
  }
}

class FirebaseDriftData extends DataClass
    implements Insertable<FirebaseDriftData> {
  final int id;
  final String texto;
  const FirebaseDriftData({required this.id, required this.texto});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['texto'] = Variable<String>(texto);
    return map;
  }

  FirebaseDriftCompanion toCompanion(bool nullToAbsent) {
    return FirebaseDriftCompanion(
      id: Value(id),
      texto: Value(texto),
    );
  }

  factory FirebaseDriftData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return FirebaseDriftData(
      id: serializer.fromJson<int>(json['id']),
      texto: serializer.fromJson<String>(json['texto']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'texto': serializer.toJson<String>(texto),
    };
  }

  FirebaseDriftData copyWith({int? id, String? texto}) => FirebaseDriftData(
        id: id ?? this.id,
        texto: texto ?? this.texto,
      );
  FirebaseDriftData copyWithCompanion(FirebaseDriftCompanion data) {
    return FirebaseDriftData(
      id: data.id.present ? data.id.value : this.id,
      texto: data.texto.present ? data.texto.value : this.texto,
    );
  }

  @override
  String toString() {
    return (StringBuffer('FirebaseDriftData(')
          ..write('id: $id, ')
          ..write('texto: $texto')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, texto);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is FirebaseDriftData &&
          other.id == this.id &&
          other.texto == this.texto);
}

class FirebaseDriftCompanion extends UpdateCompanion<FirebaseDriftData> {
  final Value<int> id;
  final Value<String> texto;
  const FirebaseDriftCompanion({
    this.id = const Value.absent(),
    this.texto = const Value.absent(),
  });
  FirebaseDriftCompanion.insert({
    this.id = const Value.absent(),
    required String texto,
  }) : texto = Value(texto);
  static Insertable<FirebaseDriftData> custom({
    Expression<int>? id,
    Expression<String>? texto,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (texto != null) 'texto': texto,
    });
  }

  FirebaseDriftCompanion copyWith({Value<int>? id, Value<String>? texto}) {
    return FirebaseDriftCompanion(
      id: id ?? this.id,
      texto: texto ?? this.texto,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (texto.present) {
      map['texto'] = Variable<String>(texto.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('FirebaseDriftCompanion(')
          ..write('id: $id, ')
          ..write('texto: $texto')
          ..write(')'))
        .toString();
  }
}

abstract class _$FirebaseDatabaseDrift extends GeneratedDatabase {
  _$FirebaseDatabaseDrift(QueryExecutor e) : super(e);
  $FirebaseDatabaseDriftManager get managers =>
      $FirebaseDatabaseDriftManager(this);
  late final $FirebaseDriftTable firebaseDrift = $FirebaseDriftTable(this);
  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities => [firebaseDrift];
}

typedef $$FirebaseDriftTableCreateCompanionBuilder = FirebaseDriftCompanion
    Function({
  Value<int> id,
  required String texto,
});
typedef $$FirebaseDriftTableUpdateCompanionBuilder = FirebaseDriftCompanion
    Function({
  Value<int> id,
  Value<String> texto,
});

class $$FirebaseDriftTableFilterComposer
    extends Composer<_$FirebaseDatabaseDrift, $FirebaseDriftTable> {
  $$FirebaseDriftTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get texto => $composableBuilder(
      column: $table.texto, builder: (column) => ColumnFilters(column));
}

class $$FirebaseDriftTableOrderingComposer
    extends Composer<_$FirebaseDatabaseDrift, $FirebaseDriftTable> {
  $$FirebaseDriftTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get texto => $composableBuilder(
      column: $table.texto, builder: (column) => ColumnOrderings(column));
}

class $$FirebaseDriftTableAnnotationComposer
    extends Composer<_$FirebaseDatabaseDrift, $FirebaseDriftTable> {
  $$FirebaseDriftTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get texto =>
      $composableBuilder(column: $table.texto, builder: (column) => column);
}

class $$FirebaseDriftTableTableManager extends RootTableManager<
    _$FirebaseDatabaseDrift,
    $FirebaseDriftTable,
    FirebaseDriftData,
    $$FirebaseDriftTableFilterComposer,
    $$FirebaseDriftTableOrderingComposer,
    $$FirebaseDriftTableAnnotationComposer,
    $$FirebaseDriftTableCreateCompanionBuilder,
    $$FirebaseDriftTableUpdateCompanionBuilder,
    (
      FirebaseDriftData,
      BaseReferences<_$FirebaseDatabaseDrift, $FirebaseDriftTable,
          FirebaseDriftData>
    ),
    FirebaseDriftData,
    PrefetchHooks Function()> {
  $$FirebaseDriftTableTableManager(
      _$FirebaseDatabaseDrift db, $FirebaseDriftTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$FirebaseDriftTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$FirebaseDriftTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$FirebaseDriftTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String> texto = const Value.absent(),
          }) =>
              FirebaseDriftCompanion(
            id: id,
            texto: texto,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required String texto,
          }) =>
              FirebaseDriftCompanion.insert(
            id: id,
            texto: texto,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$FirebaseDriftTableProcessedTableManager = ProcessedTableManager<
    _$FirebaseDatabaseDrift,
    $FirebaseDriftTable,
    FirebaseDriftData,
    $$FirebaseDriftTableFilterComposer,
    $$FirebaseDriftTableOrderingComposer,
    $$FirebaseDriftTableAnnotationComposer,
    $$FirebaseDriftTableCreateCompanionBuilder,
    $$FirebaseDriftTableUpdateCompanionBuilder,
    (
      FirebaseDriftData,
      BaseReferences<_$FirebaseDatabaseDrift, $FirebaseDriftTable,
          FirebaseDriftData>
    ),
    FirebaseDriftData,
    PrefetchHooks Function()>;

class $FirebaseDatabaseDriftManager {
  final _$FirebaseDatabaseDrift _db;
  $FirebaseDatabaseDriftManager(this._db);
  $$FirebaseDriftTableTableManager get firebaseDrift =>
      $$FirebaseDriftTableTableManager(_db, _db.firebaseDrift);
}
