import 'dart:io';

import '../models/info_device_static.dart';

class InfoDeviceData {
  InfoDeviceData._();
  static final InfoDeviceData instance = InfoDeviceData._();

  DeviceInfoStatic? deviceInfoStatic;

  void setDeviceInfoStatic(DeviceInfoStatic deviceInfoStatic) {
    this.deviceInfoStatic = deviceInfoStatic;
  }

  DeviceInfoStatic getDeviceInfoStatic() {
    return deviceInfoStatic ??
        DeviceInfoStatic(
          modelo: Platform.isIOS ? 'iPhone' : 'Desconhecido',
          name: Platform.isIOS ? 'iPhone' : '',
          tela: Platform.isIOS ? 'iPhone' : '',
          systemName: Platform.isIOS ? 'iPhone' : '',
          systemVersion: Platform.isIOS ? 'iPhone' : '',
        );
  }
}
