import 'package:flutter/foundation.dart';

import 'login/login.dart';

class ApiLs {
  ApiLs._();
  static final instance = ApiLs._();

  final ValueNotifier<bool> developmentState = ValueNotifier<bool>(kDebugMode);
  bool get development => developmentState.value;

  static const String _apiprod = 'https://apimobile.octalog.com.br';
  static const String _apidev =
      'https://api-entregadores-qa-linux.azurewebsites.net';

  String get api => development ? _apidev : _apiprod;

  void setDevelopment(bool value) => developmentState.value = value;

  List<String> usuariosTest = ["999", "1000", "3703", "99995"];

  bool mostrarTrocaApi() {
    final String usuario = Login.instance.usuarioLogado!.usuario;

    if (usuariosTest.contains(usuario)) {
      return true;
    }
    return false;
  }
}
