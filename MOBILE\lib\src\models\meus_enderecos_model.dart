import 'package:map_fields/map_fields.dart';

class MeusEnderecosModel {
  final int idAgenteEndereco;
  final int idAgente;
  final String dataInclusao;
  final String apelido;
  final String endereco;
  final String numero;
  final String bairro;
  final String cidade;
  final String cep;
  final String uf;
  final double latitude;
  final double longitude;

  MeusEnderecosModel(
      {required this.idAgenteEndereco,
      required this.idAgente,
      required this.dataInclusao,
      required this.apelido,
      required this.endereco,
      required this.numero,
      required this.bairro,
      required this.cidade,
      required this.cep,
      required this.uf,
      required this.latitude,
      required this.longitude});

  factory MeusEnderecosModel.fromJson(Map<String, dynamic> json) {
    final MapFields mapFields = MapFields.load(json);
    return MeusEnderecosModel(
        // idAgenteEndereco: (json['IDAgenteEndereco'] ?? -1) as int,
        // idAgente: json['IDAgente'],
        // dataInclusao: json['DataInclusao'],
        // apelido: (json["Apelido"] ?? '').toString(),
        // endereco: (json["Endereco"] ?? '').toString(),
        // numero: (json["Numero"] ?? '').toString(),
        // bairro: (json["Bairro"] ?? '').toString(),
        // cidade: (json["Cidade"] ?? '').toString(),
        // cep: (json["Cep"] ?? '').toString(),
        // uf: (json["UF"] ?? '').toString(),
        // latitude: (json['Latitude'] ?? 0.0),
        // longitude: (json['Longitude'] ?? 0.0)
        idAgenteEndereco: mapFields.getInt('IDAgenteEndereco', -1),
        idAgente: mapFields.getInt('IDAgente', -1),
        dataInclusao: mapFields.getString('DataInclusao', ''),
        apelido: mapFields.getString('Apelido', ''),
        endereco: mapFields.getString('Endereco', ''),
        numero: mapFields.getString('Numero', ''),
        bairro: mapFields.getString('Bairro', ''),
        cidade: mapFields.getString('Cidade', ''),
        cep: mapFields.getString('Cep', ''),
        uf: mapFields.getString('UF', ''),
        latitude: mapFields.getDouble('Latitude', 0.0),
        longitude: mapFields.getDouble('Longitude', 0.0));
  }

  String get enderecoSubtititle =>
      '$endereco - $bairro N°: $numero \n$cidade - $uf \nCEP: $cep';
}
