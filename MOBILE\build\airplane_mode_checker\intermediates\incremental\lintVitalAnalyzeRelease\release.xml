<variant
    name="release"
    package="com.u14h4i.airplane_mode_checker"
    minSdkVersion="16"
    targetSdkVersion="16"
    mergedManifest="C:\projetos\octa.log\MOBILE\build\airplane_mode_checker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml"
    proguardFiles="C:\projetos\octa.log\MOBILE\build\airplane_mode_checker\intermediates\default_proguard_files\global\proguard-android.txt-8.7.0"
    partialResultsDir="C:\projetos\octa.log\MOBILE\build\airplane_mode_checker\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ccb22fb726aba7a94ea3cbe0eb4bdac7\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\main\kotlin;src\release\java;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="C:\projetos\octa.log\MOBILE\build\airplane_mode_checker\intermediates\javac\release\compileReleaseJavaWithJavac\classes;C:\projetos\octa.log\MOBILE\build\airplane_mode_checker\tmp\kotlin-classes\release;C:\projetos\octa.log\MOBILE\build\airplane_mode_checker\intermediates\compile_r_class_jar\release\generateReleaseRFile\R.jar"
      type="MAIN"
      applicationId="com.u14h4i.airplane_mode_checker"
      generatedSourceFolders="C:\projetos\octa.log\MOBILE\build\airplane_mode_checker\generated\ap_generated_sources\release\out"
      generatedResourceFolders="C:\projetos\octa.log\MOBILE\build\airplane_mode_checker\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ccb22fb726aba7a94ea3cbe0eb4bdac7\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
