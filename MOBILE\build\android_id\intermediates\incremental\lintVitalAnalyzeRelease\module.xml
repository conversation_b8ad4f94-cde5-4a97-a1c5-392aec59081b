<lint-module
    format="1"
    dir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\android_id-0.4.0\android"
    name=":android_id"
    type="LIBRARY"
    maven="dev.fluttercommunity.android_id:android_id:1.0-SNAPSHOT"
    agpVersion="8.7.0"
    buildFolder="C:\projetos\octa.log\MOBILE\build\android_id"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\sdk\platforms\android-34\android.jar;C:\Users\<USER>\AppData\Local\Android\sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="17"
    compileTarget="android-34"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
