// import 'package:flutter/material.dart';
// import 'package:flutter_cached_pdfview/flutter_cached_pdfview.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'package:url_launcher/url_launcher.dart';

// import '../../../components/buttom_ls/button_ls_custom.dart';
// import '../../../components/loading_ls/loading_ls.dart';
// import '../../../utils/colors.dart';
// import '../cadastro_state.dart';
// import '../cadastro_store.dart';
// import '../components/card_pestrador.dart';

// class ContratoPage extends StatefulWidget {
//   final CadastroStore store;
//   const ContratoPage({
//     super.key,
//     required this.store,
//   });

//   @override
//   State<ContratoPage> createState() => _ContratoPageState();
// }

// class _ContratoPageState extends State<ContratoPage> {

//   @override
//   void initState() {
//     widget.store.buscarContatrato(context);
//     super.initState();
//   }
  
//   @override
//   Widget build(BuildContext context) {
//     return SafeArea(
//       child: Scaffold(
//         body: ValueListenableBuilder<CadastroState>(
//             valueListenable: widget.store.state,
//             builder: (context, state, _) {
//               final store = widget.store;
//               if (state.isLoadingcarregartela) {
//                 return const Center(
//                   child: LoadingLs(),
//                 );
//               }
//               return Column(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 crossAxisAlignment: CrossAxisAlignment.center,
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   CardPrestador(
//                     idTipoAgente: state.contratoModel?.idTipoAgente ?? 0,
//                     nomePrestador: state.contratoModel?.prestador ?? '',
//                   ),
//                   Expanded(
//                     child: Container(
//                       width: MediaQuery.of(context).size.width,
//                       height: MediaQuery.of(context).size.height,
//                       decoration: BoxDecoration(
//                         color: const Color.fromARGB(255, 255, 255, 255),
//                         borderRadius: BorderRadius.circular(5),
//                         border: Border.all(
//                           color: ColorsCustom.customBlack,
//                           width: 2,
//                         ),
//                       ),
//                       child: Builder(
//                         builder: (context) {
//                           return PDF(
//                             fitEachPage: true,
//                             pageFling: true,
//                             preventLinkNavigation: false,
//                             onRender: (pages) {
//                               print('rendered: $pages');
//                             },
//                           ).cachedFromUrl(
//                             state.contratoModel?.contrato ?? '',
//                             maxAgeCacheObject: const Duration(days: 1),
//                             placeholder: (progress) => Center(
//                               child: Column(
//                                 mainAxisAlignment: MainAxisAlignment.center,
//                                 crossAxisAlignment: CrossAxisAlignment.center,
//                                 mainAxisSize: MainAxisSize.min,
//                                 children: [
//                                   Text(
//                                     '$progress %',
//                                     style: const TextStyle(
//                                       color: ColorsCustom.customOrange,
//                                       fontSize: 22,
//                                     ),
//                                   ),
//                                   const LoadingLs(),
//                                 ],
//                               ),
//                             ),
//                             errorWidget: (error) {
//                               print(error);
//                               return const Center(
//                                 child: Text("Erro ao carregar o pdf"),
//                               );
//                             },
//                           );
//                         },
//                       ),
//                     ),
//                   ),
//                   Padding(
//                     padding: const EdgeInsets.symmetric(
//                       vertical: 10,
//                     ),
//                     child: Row(
//                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                       children: [
//                         Expanded(
//                           flex: 2,
//                           child: Text(
//                             "Leia o contrato para aceitar os termos",
//                             style: GoogleFonts.roboto(
//                               fontSize: 11,
//                               color: ColorsCustom.customBlack,
//                             ),
//                           ),
//                         ),
//                         GestureDetector(
//                           onTap: () async {

//                             final url =
//                                 Uri.parse(state.contratoModel?.contrato ?? '');
//                             if (await canLaunchUrl(url)) {
//                               await launchUrl(
//                                 url,
//                                 mode: LaunchMode.externalApplication,
//                               );
//                             } else {
//                               throw 'Could not launch $url';
//                             }
//                           },
//                           child: Text(
//                             "Download do contrato",
//                             style: GoogleFonts.roboto(
//                               fontSize: 12,
//                               fontWeight: FontWeight.w500,
//                               color: ColorsCustom.customBlack,
//                             ),
//                           ),
//                         )
//                       ],
//                     ),
//                   ),
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       Checkbox(
//                         activeColor: ColorsCustom.customOrange,
//                         value:
//                             state.contratoModel?.dataConfirmouCadastro != null,
//                         onChanged: (value) {
//                           final dataAceite = DateTime.now();
                         
//                           if (state.contratoModel?.dataConfirmouCadastro !=
//                               null) {
//                             store.setContratoModelParte(
//                                 dataConfirmouCadastro: dataAceite);
//                             setState(() {});
//                           }
//                         },
//                       ),
//                       const Expanded(
//                         child: Text(
//                           "Estou de acordo com os termos de serviço",
//                           style: TextStyle(
//                             fontSize: 11,
//                             color: Colors.black,
//                             fontWeight: FontWeight.w400,
//                           ),
//                         ),
//                       ),
//                     ],
//                   ),
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.center,
//                     crossAxisAlignment: CrossAxisAlignment.center,
//                     children: [
//                       Expanded(
//                         child: ButtonLsCustom(
//                           text: 'PRÓXIMO',
//                           colorBackground: ColorsCustom.customOrange,
//                           onPressed:
//                               state.contratoModel?.dataConfirmouCadastro != null
//                                   ? () async {
//                                       store.setActivaPage(1);
//                                     }
//                                   : null,
//                         ),
//                       ),
//                     ],
//                   ),
//                   const SizedBox(height: 10),
//                 ],
//               );
//             }),
//       ),
//     );
//   }
// }
