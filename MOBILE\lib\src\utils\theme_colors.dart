import 'package:flutter/material.dart';

/// Classe helper para acessar cores do tema atual
/// Substitui o uso direto de ColorsCustom por cores baseadas no Theme
class ThemeColors {
  static ColorScheme of(BuildContext context) {
    return Theme.of(context).colorScheme;
  }

  // Cores principais baseadas no tema
  static Color primary(BuildContext context) {
    return Theme.of(context).colorScheme.primary;
  }

  static Color secondary(BuildContext context) {
    return Theme.of(context).colorScheme.secondary;
  }

  static Color surface(BuildContext context) {
    return Theme.of(context).colorScheme.surface;
  }

  static Color onSurface(BuildContext context) {
    return Theme.of(context).colorScheme.onSurface;
  }

  static Color error(BuildContext context) {
    return Theme.of(context).colorScheme.error;
  }

  // Cores específicas mapeadas para o tema
  static Color customWhite(BuildContext context) {
    return Colors.white;
  }

  static Color customBlack(BuildContext context) {
    return Theme.of(context).colorScheme.onSurface;
  }

  static Color customGrey(BuildContext context) {
    return Colors.grey;
  }

  static Color customGreyBold(BuildContext context) {
    return const Color(0xFF404040);
  }

  static Color customGreyLight(BuildContext context) {
    return Colors.grey.shade300;
  }

  static Color customGreen(BuildContext context) {
    return Theme.of(context).colorScheme.secondary;
  }

  static Color customOrange(BuildContext context) {
    return Theme.of(context).colorScheme.primary;
  }

  static Color customRed(BuildContext context) {
    return Theme.of(context).colorScheme.error;
  }

  static Color customTransparent(BuildContext context) {
    return Colors.transparent;
  }

  static Color customBlackWhite(BuildContext context) {
    return const Color(0xFF383838);
  }

  static Color customYellow(BuildContext context) {
    return const Color(0xFFf4d03f);
  }

  static Color customBlue(BuildContext context) {
    return const Color.fromARGB(255, 199, 230, 250);
  }
}

/// Extension para facilitar o acesso às cores do tema
extension ThemeColorsExtension on BuildContext {
  ColorScheme get colors => Theme.of(this).colorScheme;
  
  Color get primaryColor => Theme.of(this).colorScheme.primary;
  Color get secondaryColor => Theme.of(this).colorScheme.secondary;
  Color get surfaceColor => Theme.of(this).colorScheme.surface;
  Color get onSurfaceColor => Theme.of(this).colorScheme.onSurface;
  Color get errorColor => Theme.of(this).colorScheme.error;
  
  // Cores customizadas mapeadas
  Color get customWhite => ThemeColors.customWhite(this);
  Color get customBlack => ThemeColors.customBlack(this);
  Color get customGrey => ThemeColors.customGrey(this);
  Color get customGreyBold => ThemeColors.customGreyBold(this);
  Color get customGreyLight => ThemeColors.customGreyLight(this);
  Color get customGreen => ThemeColors.customGreen(this);
  Color get customOrange => ThemeColors.customOrange(this);
  Color get customRed => ThemeColors.customRed(this);
  Color get customTransparent => ThemeColors.customTransparent(this);
  Color get customBlackWhite => ThemeColors.customBlackWhite(this);
  Color get customYellow => ThemeColors.customYellow(this);
  Color get customBlue => ThemeColors.customBlue(this);
}
