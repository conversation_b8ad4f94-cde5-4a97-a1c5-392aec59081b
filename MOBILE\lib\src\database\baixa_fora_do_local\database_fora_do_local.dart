import 'package:hive/hive.dart';
import 'package:logger/logger.dart';
import 'package:map_fields/map_fields.dart';

class ForaDoLocalModel {
  final int idos;
  final DateTime dataInclusao;
  final bool isTimeCompleto;
  final double latitude;
  final double longitude;

  ForaDoLocalModel({
    required this.idos,
    required this.dataInclusao,
    required this.isTimeCompleto,
    required this.latitude,
    required this.longitude,
  });

  factory ForaDoLocalModel.fromMap(Map<String, dynamic> map) {
    final f = MapFields.load(map);
    return ForaDoLocalModel(
      idos: f.getInt('idos', 0),
      dataInclusao: f.getDateTime('dataInclusao', DateTime.now()),
      isTimeCompleto: f.getBool('isTimeCompleto', false),
      latitude: f.getDouble('latitude', 0),
      longitude: f.getDouble('longitude', 0),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'idos': idos,
      'dataInclusao': dataInclusao.toIso8601String(),
      'isTimeCompleto': isTimeCompleto,
      'latitude': latitude,
      'longitude': longitude,
    };
  }
}

abstract class GravarBaixaForaDoLocalDatabase {
  static final instance = GravarBaixaForaDoLocalDatabaseImpl();

  Future<void> initGravarBaixaForaDoLocal();
  Future<void> setGravarBaixaForaDoLocal(ForaDoLocalModel value);
  Future<void> regravarBaixaForaDoLocal(ForaDoLocalModel value);
  Future<List<ForaDoLocalModel>> getGravarBaixaForaDoLocal();
  Future<void> deleteGravarBaixaForaDoLocalDiaAnterior();
  Future<void> deletAll();
}

class GravarBaixaForaDoLocalDatabaseImpl
    implements GravarBaixaForaDoLocalDatabase {
  late Box _box;
  bool initialized = false;

  @override
  Future<void> initGravarBaixaForaDoLocal() async {
    if (initialized) return;
    Logger().i('initGravarBaixaForaDoLocal');
    try {
      _box = await Hive.openBox(
        'baixa_fora_do_local',
        compactionStrategy: (int total, int deleted) {
          return deleted > 20;
        },
      );
      initialized = true;
    } catch (e) {
      Logger().e('Error initializing Hive box: $e');
    }
  }

  @override
  Future<void> setGravarBaixaForaDoLocal(ForaDoLocalModel value) async {
    await initGravarBaixaForaDoLocal();
    if (!initialized) {
      throw Exception('Hive box is not initialized');
    }
    await _box.put(value.idos, value.toMap());
  }

  @override
  Future<void> regravarBaixaForaDoLocal(ForaDoLocalModel value) async {
    await initGravarBaixaForaDoLocal();
    if (!initialized) {
      throw Exception('Hive box is not initialized');
    }
    await _box.put(value.idos, value.toMap());
  }

  @override
  Future<List<ForaDoLocalModel>> getGravarBaixaForaDoLocal() async {
    await initGravarBaixaForaDoLocal();
    if (!initialized) {
      throw Exception('Hive box is not initialized');
    }
    final data = _box.values.toList();
    final dataMap = data.map((e) {
      final map = Map<String, dynamic>.from(e);
      return ForaDoLocalModel.fromMap(map);
    }).toList();
    final dataMapToday = dataMap.where((element) {
      final dataAtual = DateTime.now();
      final dataInclusao = element.dataInclusao;
      return dataAtual.year == dataInclusao.year &&
          dataAtual.month == dataInclusao.month &&
          dataAtual.day == dataInclusao.day;
    }).toList();
    return dataMapToday;
  }

  @override
  Future<void> deleteGravarBaixaForaDoLocalDiaAnterior() async {
    await initGravarBaixaForaDoLocal();
    if (!initialized) {
      throw Exception('Hive box is not initialized');
    }
    final data = _box.values.toList();
    final dataMap = data.map((e) {
      final map = Map<String, dynamic>.from(e);
      return ForaDoLocalModel.fromMap(map);
    }).toList();
    final dataMapDiaAnterior = dataMap.where((element) {
      final dataAtual = DateTime.now();
      final dataInclusao = element.dataInclusao;
      return dataAtual.difference(dataInclusao).inDays > 0;
    }).toList();
    for (var element in dataMapDiaAnterior) {
      await _box.delete(element.idos);
    }
  }

  @override
  Future<void> deletAll() async {
    await initGravarBaixaForaDoLocal();
    if (!initialized) {
      throw Exception('Hive box is not initialized');
    }
    await _box.clear();
  }
}
