import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:octalog/src/pages/mapa_page/mapa_page_state.dart';

import '../../pages/mapa_page/mapa_page_store.dart';
import '../../utils/colors.dart';

class MapWidget extends StatefulWidget {
  const MapWidget({super.key});

  @override
  State<MapWidget> createState() => _MapWidgetState();
}

class _MapWidgetState extends State<MapWidget> {
  final MapaPageStore store = MapaPageStore();

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<MapaPageState>(
      valueListenable: store.state,
      builder: (BuildContext context, MapaPageState value, Widget? child) {
        final loc = value.latLng;
        final mapaPageController = value.mapaPageController;

        return FlutterMap(
          mapController: mapaPageController,
          options: MapOptions(
            enableMultiFingerGestureRace: false,
            enableScrollWheel: false,
            rotation: 0,
            center: loc,
            zoom: 16.5,
          ),
          nonRotatedChildren: const [
            Positioned(
              bottom: 5,
              right: 5,
              child: Text("Octalog"),
            ),
          ],
          children: [
            TileLayer(
              urlTemplate: "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
              subdomains: const ['a', 'b', 'c'],
            ),
            MarkerLayer(
              markers: [
                Marker(
                  width: 80.0,
                  height: 80.0,
                  point: loc,
                  builder: (ctx) => GestureDetector(
                    onTap: () {},
                    child: const Icon(
                      Icons.location_on,
                      color: ColorsCustom.customRed,
                      size: 40.0,
                    ),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }
}
