import 'package:map_fields/map_fields.dart';

class Vei<PERSON>lo {
  final String nome;
  final int id;

  Vei<PERSON>lo({
    required this.nome,
    required this.id,
  });

  factory Veiculo.fromJson(Map<String, dynamic> json) {
    final MapFields mapFields = MapFields.load(json);

    return Veiculo(
      nome: mapFields.getString('Nome', ''),
      id: mapFields.getInt('IDTipoVeiculo', 0),
    );
  }

  static List<Veiculo> fromJsonList(List<dynamic> json) {
    final veiculos = json.map((e) => Veiculo.fromJson(e)).toList();
    veiculos.add(Veiculo(nome: 'Selecione', id: 0));
    veiculos.sort((a, b) => a.id.compareTo(b.id));
    return veiculos;
  }

  Map<String, dynamic> toJson() {
    return {
      'Nome': nome,
      'IDTipoVeiculo': id,
    };
  }
}
