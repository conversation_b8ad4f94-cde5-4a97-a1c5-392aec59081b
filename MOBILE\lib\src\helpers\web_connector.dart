import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:android_id/android_id.dart';
import 'package:asuka/asuka.dart' as asuka;
import 'package:azstore/azstore.dart';
import 'package:camera_camera/camera_camera.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:octalog/src/utils/extesion.dart';
import 'package:octalog/src/utils/offline_helper.dart';
import 'package:octalog/src/utils/verify_gps.dart';
import 'package:octalog/src/utils/versao.dart';
import 'package:octalog/src/utils/virifica_dados_moveis.dart';
import 'package:mime/mime.dart';
import 'package:ntp/ntp.dart';
import 'package:path/path.dart';

import '../../errors.dart';
//import '../../remote_config_wrapper.dart';
import '../components/fcm_alert_dailog/fcm_external/fcm_external_login.dart';
import '../database/config_blob/config_database.dart';
import '../database/log_database/log_database.dart';
import '../database/offline_request/offline_request_database.dart';
import '../pages/home/<USER>';
import 'another.dart';
import 'gps/gps_contract.dart';
import 'login/login.dart';

class WebConnector {
  WebConnector();

  final _offlineCodes = [502, 400, 408, 501, 444, 423, 417, 111, 422];
  String imeiNo = '';

  Future<T> _treatment<T>(Future<T> Function() func, String endpoint, {bool setOfflineIfError = true}) async {
    try {
      await verificarGPS();
      final resp = await func();
      return resp;
    } catch (e) {
      if (e is DioException) {
        final status = e.response?.statusCode;
        if (status == 304) {
          return Response(data: e.response?.data, statusCode: status, requestOptions: e.requestOptions) as T;
        }
        if (setOfflineIfError && (_offlineCodes.contains(status) || e.error is SocketException)) {
          offlineStore.setOffline(true);
        }
        throw ConnectionError(
          status ?? 0,
          e.response?.data is Map ? jsonEncode(e.response?.data) : e.response?.data.toString() ?? e.message ?? '',
          e.toString(),
          endpoint,
        );
      }
      rethrow;
    }
  }

  Future<String> getImei() async {
    try {
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      if (Platform.isAndroid) {
        const androidIdPlugin = AndroidId();
        imeiNo = (await androidIdPlugin.getId()) ?? 'SEM IMEI';
      } else if (Platform.isIOS) {
        imeiNo = (await deviceInfo.iosInfo).identifierForVendor ?? 'SEM IMEI';
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return imeiNo;
  }

  // Dio get _dio => Dio(
  //       BaseOptions(baseUrl: RemoteConfigWrapper.i.urlApi),
  //     );

  Future<void> reset() async {
    const url = '/agente/reset';
    final dio = Dio(BaseOptions(baseUrl: getUrlBase(url)));
    await dio.get('/agente/reset');
  }

  String getUrlBase(String endpoint) {
    return "https://api-mobile-entregadores.octalog.com.br/";
    //"https://api-entregadores-eycrhcg5afcsbmfm.brazilsouth-01.azurewebsites.net/";
  }

  Future<Map<String, dynamic>> _getHeaders(Map<String, dynamic>? headers) async {
    final position = await GpsHelperContract.instance.updateAndGetLastPosition();
    final sincronismoRestante = await OfflineRequestDatabase.instance.getQtdEnventosRestantes();
    final connectivityResult = await (Connectivity().checkConnectivity());
    final statusGps = GpsHelperContract.instance.serviceEnabled;
    final permissaoGpsApp = GpsHelperContract.instance.permissionGranted;

    final ConnectivityChecker connectivityChecker = ConnectivityChecker.instance;

    final dadosMoveis = await connectivityChecker.checkDadosMoveis();

    bool? wifi = await connectivityChecker.checkWifi();
    bool? modoAviao = await connectivityChecker.checkModoAviao();
    bool? modeDesenvolvedor = await connectivityChecker.checkDeveloperMode();

    final headersi = {
      'Token': Login.instance.usuarioLogado?.token ?? '',
      'IMEI': imeiNo,
      'APPVersao': VersaoApp.string,
      'FCMKey': FmcExternalLogin.fcmKey,
      'Latitude': position?.latitude,
      'Longitude': position?.longitude,
      'StatusGps': statusGps,
      'PermissaoGpsApp': permissaoGpsApp,
      'Disponivel': HomeController.instance.state.value.isOnline,
      'SincronismoRestante': sincronismoRestante == 1 ? 0 : sincronismoRestante,
      'DataEvento': DateTime.now().dataHoraServidorFomart.toIso8601String(),
      'StatusDadosMoveis': dadosMoveis,
      'StatusWifi': wifi,
      'StatusModoAviao': modoAviao,
      'StatusModoDesenvolvedor': modeDesenvolvedor,
      'UsandoVPN': connectivityResult == ConnectivityResult.vpn,
      ...?headers,
    };
    final token = Login.instance.usuarioLogado?.token ?? '';
    if (token.isNotEmpty) {
      headersi['Token'] = token;
    }

    return headersi;
  }

  Future<Response> get(String url, {Map<String, dynamic>? headers, Map<String, dynamic>? queryParameters}) async {
    return await _treatment(() async {
      if (imeiNo.isEmpty) {
        await getImei();
      }
      final headersi = await _getHeaders(headers);
      offlineStore.setOffline(false);
      final dio = Dio(BaseOptions(baseUrl: getUrlBase(url)));
      return await dio.get(url, options: Options(headers: headersi), queryParameters: queryParameters);
    }, url);
  }

  // getSimples
  Future<Response> getSimples(String url, {Map<String, dynamic>? headers, Map<String, dynamic>? body, Map<String, dynamic>? queryParameters}) async {
    return await _treatment(() async {
      offlineStore.setOffline(false);
      final dio = Dio(BaseOptions(baseUrl: getUrlBase(url)));
      return await dio.get(url, options: Options(headers: {'Token': Login.instance.usuarioLogado?.token ?? ''}), data: body, queryParameters: queryParameters);
    }, url);
  }

  // postSimples
  Future<Response> postSimples(String url, {Map<String, dynamic>? headers, dynamic body, Map<String, dynamic>? queryParameters}) async {
    return await _treatment(() async {
      offlineStore.setOffline(false);
      final dio = Dio(BaseOptions(baseUrl: getUrlBase(url)));
      return await dio.post(url, options: Options(headers: {'Token': Login.instance.usuarioLogado?.token ?? ''}), data: body, queryParameters: queryParameters);
    }, url);
  }

  Future<Response> post(String url, {Map<String, dynamic>? headers, dynamic body, Map<String, dynamic>? queryParameters, bool setOfflineIfError = true}) async {
    return await _treatment(
      () async {
        if (imeiNo.isEmpty) {
          await getImei();
        }
        final headersi = await _getHeaders(headers);
        final dio = Dio(BaseOptions(baseUrl: getUrlBase(url)));
        offlineStore.setOffline(false);
        return await dio.post(url, options: Options(headers: headersi), data: body, queryParameters: queryParameters);
      },
      url,
      setOfflineIfError: setOfflineIfError,
    );
  }

  Future<Response> put(String url, {Map<String, dynamic>? headers, dynamic body}) async {
    return await _treatment(() async {
      if (imeiNo.isEmpty) {
        await getImei();
      }
      final headersi = await _getHeaders(headers);
      offlineStore.setOffline(false);
      final dio = Dio(BaseOptions(baseUrl: getUrlBase(url)));
      return await dio.put(url, options: Options(headers: headersi), data: body);
    }, url);
  }

  Future<Response> delete(String url, {Map<String, dynamic>? headers, dynamic body, Map<String, dynamic>? queryParameters}) async {
    return await _treatment(() async {
      if (imeiNo.isEmpty) {
        await getImei();
      }
      final headersi = await _getHeaders(headers);
      offlineStore.setOffline(false);
      final dio = Dio(BaseOptions(baseUrl: getUrlBase(url)));
      return await dio.delete(url, options: Options(headers: headersi), data: body, queryParameters: queryParameters);
    }, url);
  }

  // request with text method
  Future<Response> request(String url, {Map<String, String>? headers, required String method, dynamic body, Map<String, dynamic>? params}) async {
    return await _treatment(() async {
      if (imeiNo.isEmpty) {
        await getImei();
      }
      final headersi = await _getHeaders(headers);
      offlineStore.setOffline(false);
      final dio = Dio(BaseOptions(baseUrl: getUrlBase(url)));
      return await dio.request(url, options: Options(method: method, headers: headersi), data: body, queryParameters: params);
    }, url);
  }

  Future<String?> postPdfBlobStorage({required String fileName, required Uint8List content, bool isAgentDocument = false}) async {
    final config = await ConfigDatabase.instance.getConfig();

    if (!fileName.toLowerCase().endsWith('.pdf')) {
      fileName += '.pdf';
    }

    final storage = AzureStorage.parse(config.url);
    String container = config.fotoAgente;
    await storage.putBlob('/$container/$fileName', bodyBytes: content, contentType: 'application/pdf', type: BlobType.blockBlob);
    final retorno = 'https://${storage.config['AccountName']}.blob.core.windows.net/$container/$fileName';

    return retorno;
  }

  Future<String?> postImageBlobStorage({required String fileName, required String contentType, required Uint8List content, bool fotoAgente = false}) async {
    final config = await ConfigDatabase.instance.getConfig();
    String container = fotoAgente ? config.fotoAgente : config.fotoFachada;
    final storage = AzureStorage.parse(config.url);
    await storage.putBlob('/$container/$fileName', bodyBytes: content, contentType: contentType, type: BlobType.blockBlob);
    final retorno = 'https://${storage.config['AccountName']}.blob.core.windows.net/$container/$fileName';
    return retorno;
  }

  Future<XFile?> tirarFoto(BuildContext context, [ImageSource? source]) async {
    File? image;
    return await _treatment(() async {
      if (imeiNo.isEmpty) {
        await getImei();
      }
      await Navigator.push(
        context,
        MaterialPageRoute(
          builder:
              (_) => Scaffold(
                appBar: AppBar(
                  leading: IconButton(
                    icon: Container(
                      height: 40,
                      width: 40,
                      decoration: BoxDecoration(shape: BoxShape.circle, color: Colors.orange.withOpacity(0.1)),
                      child: const Icon(Icons.arrow_back, color: Colors.orange),
                    ),
                    onPressed: () {
                      Navigator.pop(context);
                    },
                  ),
                  title: const Text('Tirar Foto'),
                  centerTitle: true,
                ),
                body: CameraCamera(
                  resolutionPreset: ResolutionPreset.medium,
                  onFile: (file) {
                    image = file;
                    Navigator.pop(context);
                  },
                ),
              ),
        ),
      );
      if (image == null) return null;

      return XFile(image!.path);
    }, 'tirarFoto()');
  }

  Future<String?> uploadImageBlobStorage({bool fotoAgente = false, ImageSource? source}) async {
    return await _treatment(() async {
      if (imeiNo.isEmpty) {
        await getImei();
      }
      final ImagePicker picker = ImagePicker();
      final image = await picker.pickImage(source: source ?? ImageSource.camera, imageQuality: 50, maxWidth: 720, maxHeight: 1280);
      if (image == null) return null;
      final File imageFile = File(image.path);
      final rnd = Random();
      final rand = rnd.nextInt(1000000);

      final fileNameBase = basename(imageFile.path);
      final fileName = '${DateTime.now().millisecondsSinceEpoch}_$rand${extension(fileNameBase)}';
      Uint8List content = await imageFile.readAsBytes();
      String contentType = lookupMimeType(basename(fileNameBase)) ?? '';
      offlineStore.setOffline(false);
      return await postImageBlobStorage(fileName: fileName, contentType: contentType, content: content, fotoAgente: fotoAgente);
    }, 'uploadImageBlobStorage()');
  }

  static Future<Another<DateTime>> getDateTimeGlobalFromWeb(DateTime now) async {
    try {
      // final dio = Dio();
      // final response = await dio
      //     .get('https://www.timeapi.io/api/TimeZone/zone?timeZone=Etc/GMT')
      //     .timeout(const Duration(seconds: 3));
      // final f = MapFields.load(response.data);
      // String timeStr = f.getString('currentLocalTime', '');
      // DateTime time = now;
      // if (timeStr.isNotEmpty) {
      //   timeStr = "${timeStr}Z";
      //   time = DateTime.tryParse(timeStr) ?? now;
      // }
      final time = await NTP.now(timeout: const Duration(seconds: 3));
      return Success(time);
    } catch (e) {
      final error = Error<DateTime>(e);
      await LogDatabase.instance.logError('DataHora', '', 'Falha ao verificar data e hora', {"error": error.value.message, "data_atual": now});
      return error;
    }
  }

  Future verificarGPS() async {
    final gpsAtivo = await GpsHelperContract.instance.checaGpsLigado();

    if (!gpsAtivo) {
      await asuka.Asuka.showDialog(builder: (ctx) => const VerifyGps());
    }
  }
}
