import 'package:map_fields/map_fields.dart';

class SelfieAgente {
  final int? iDAgentesSelfInfo;
  final String? foto;
  SelfieAgente({
    this.foto,
    this.iDAgentesSelfInfo,
  });

  factory SelfieAgente.fromJson(Map<String, dynamic> json) {
    final MapFields s = MapFields.load(json);
    return SelfieAgente(
      iDAgentesSelfInfo: s.getIntNullable('IDAgentesSelfInfo'),
      foto: s.getStringNullable('Foto'),
    );
  }
}
