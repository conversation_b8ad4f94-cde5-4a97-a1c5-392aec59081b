import 'dart:async';
import 'dart:developer';

import 'package:asuka/asuka.dart' as asuka;
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
//import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:octalog/src/database/deslocamento_database/deslocamento_database.dart';
import 'package:octalog/src/pages/home/<USER>';
import 'package:map_fields/map_fields.dart';

import 'audio_play.dart';

@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // try {
  //   FirebaseDatabaseDrift.instance;
  //   await Firebase.initializeApp(
  //       options: DefaultFirebaseOptions.currentPlatform);
  //   await setupFlutterNotifications();

  //   final path = await getApplicationDocumentsDirectory();
  //   await Hive.initFlutter(path.path);

  //   await LogDatabase.instance.initLogs();
  // } catch (_) {}

  // await FcmDataBase.instance.saveFcm(message);
  showFlutterNotification(message);
  log('Handling a background message ${message.messageId}');
}

//late AndroidNotificationChannel channel;

Future<void> setupFlutterNotifications() async {
 // flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
  // channel = const AndroidNotificationChannel(
  //   'high_importance_channel',
  //   'High Importance Notifications',
  //   description: 'This channel is used for important notifications.',
  //   importance: Importance.high,
  //   ledColor: Colors.transparent,
  //   playSound: true,
  //   sound: RawResourceAndroidNotificationSound('notification'),
  //   enableVibration: true,
  //   showBadge: true,
  // );

  // await flutterLocalNotificationsPlugin
  //     .resolvePlatformSpecificImplementation<
  //         AndroidFlutterLocalNotificationsPlugin>()
  //     ?.createNotificationChannel(channel);

  // await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
  //   alert: true,
  //   badge: true,
  //   sound: true,
  // );
}

void showFlutterNotification(RemoteMessage message) {
  RemoteNotification? notification = message.notification;
  if (notification != null && !kIsWeb) {
    try {
      playAudio();
    } catch (e) {
      log(e.toString());
    }
    // flutterLocalNotificationsPlugin.show(
    //   notification.hashCode,
    //   notification.title,
    //   notification.body,
    //   NotificationDetails(
    //     android: AndroidNotificationDetails(
    //       channel.id,
    //       channel.name,
    //       channelDescription: channel.description,
    //       enableVibration: true,
    //       icon: '@drawable/launcher_icon',
    //       largeIcon:
    //           const DrawableResourceAndroidBitmap('@drawable/launcher_icon'),
    //       playSound: true,
    //       sound: const RawResourceAndroidNotificationSound('notification'),
    //       importance: Importance.max,
    //       priority: Priority.max,
    //       showWhen: true,
    //       styleInformation: const DefaultStyleInformation(true, true),
    //     ),
    //   ),
    // );
  }
}

Future<void> showFlutterNotificationOpened(
  RemoteMessage message, [
  String? hashCodeMessage,
]) async {
  RemoteNotification? notification = message.notification;
  if (notification != null && !kIsWeb) {
    final f = MapFields.load(message.data);
    final ativo = f.getBool('ativo', true);
    final uberizado = f.getBool('uberizado', false);
    final mostrarMensagem = ativo && !uberizado && message.data.keys.isEmpty;

    if (ativo) {
      HomeController.instance.fetchAtividades();
    } else {
      final id = f.getInt('id', 0);
      await DeslocamentoDatabase.instance.deleteById(id);
      // if (hashCodeMessage != null) {
      //   await FcmDataBase.instance.deleteFcm(hashCodeMessage);
      // }
      HomeController.instance.fetchAtividades();
    }

    if (mostrarMensagem) {
      try {
        playAudio();
      } catch (e) {
        log(e.toString());
      }
      asuka.AsukaSnackbar.info(message.notification?.body ?? '').show();
      // if (hashCodeMessage != null) {
      //   await FcmDataBase.instance.deleteFcm(hashCodeMessage);
      // }
    }
  }
}

//late FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin;
