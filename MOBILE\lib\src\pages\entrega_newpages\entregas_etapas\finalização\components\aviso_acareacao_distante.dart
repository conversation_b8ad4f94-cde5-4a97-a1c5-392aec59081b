import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:asuka/asuka.dart' as asuka;
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:latlong2/latlong.dart';
import 'package:octalog/src/components/fcm_alert_dailog/components/fcm_deslocamento_widget.dart';
import 'package:octalog/src/components/fcm_alert_dailog/components/fcm_distance_data.dart';
import 'package:octalog/src/components/loading_ls/loading_ls.dart';
import 'package:octalog/src/database/config_blob/config_database.dart';
import 'package:octalog/src/database/envio_erro/envio_erro.dart';
import 'package:octalog/src/helpers/gps/gps_contract.dart';
import 'package:octalog/src/helpers/web_connector.dart';
import 'package:octalog/src/models_new/cliente_new.dart';
import 'package:octalog/src/pages/entrega_newpages/componentes/elevated_ls_button.dart';
import 'package:octalog/src/pages/home/<USER>';
import 'package:octalog/src/pages/mapa_page/mapa_page_state.dart';
import 'package:octalog/src/pages/mapa_page/mapa_page_store.dart';
import 'package:map_fields/map_fields.dart';

import '../../../../../utils/colors.dart';

Future<bool> checkDistancia(bool acareacao, ClienteNew atividade) async {
  final config = await ConfigDatabase.instance.getConfig();
  bool pausar = false;

  if (!acareacao) return pausar;

  if (config.bloqueioAcareacaoForaDoLocal == false) return false;
  if (atividade.info?.latitudeentrega == null ||
      atividade.info?.latitudeentrega == 0 ||
      atividade.info?.latitudecliente == null ||
      atividade.info?.latitudecliente == 0) {
    return pausar;
  }

  await GpsHelperContract.instance.updateAndGetLastPosition();

  final position = await GpsHelperContract.instance.updateAndGetLastPosition();
  final int distanciaMax = config.distanciaMetrosFotoFaixada ?? 300;

  if (position == null ||
      atividade.info?.latitudeentrega == null ||
      atividade.info?.longitudeentrega == null) {
    log("Acareacao: Posição do agente é nula");
    await enviarErro(erro: true, metodo: "Acareacao", jsonBody: {
      "distancia": 0,
      "distanciaMax": distanciaMax,
      "latitudeAgente": null,
      "longitudeAgente": null,
      "latitudeentrega": atividade.info?.latitudeentrega,
      "longitudeentrega": atividade.info?.longitudeentrega,
      "latitudecliente": atividade.info?.latitudecliente,
      "longitudecliente": atividade.info?.longitudecliente,
      "data": DateTime.now().toIso8601String()
    });
    return pausar;
  }

  var distanciaAgenteLocal = GeolocatorPlatform.instance.distanceBetween(
    position.latitude,
    position.longitude,
    atividade.info?.latitudeentrega ?? 0,
    atividade.info?.longitudeentrega ?? 0,
  );

  if (distanciaAgenteLocal > distanciaMax) {
    distanciaAgenteLocal = GeolocatorPlatform.instance.distanceBetween(
      position.latitude,
      position.longitude,
      atividade.info?.latitudecliente ?? 0,
      atividade.info?.longitudecliente ?? 0,
    );
  }

  if (distanciaAgenteLocal > distanciaMax) {
    final conn = WebConnector();
    await enviarErro(erro: true, metodo: "Acareacao", jsonBody: {
      "distancia": distanciaAgenteLocal,
      "distanciaMax": distanciaMax,
      "latitudeAgente": position.latitude,
      "longitudeAgente": position.longitude,
      "latitudeentrega": atividade.info?.latitudeentrega,
      "longitudeentrega": atividade.info?.longitudeentrega,
      "latitudecliente": atividade.info?.latitudecliente,
      "longitudecliente": atividade.info?.longitudecliente,
      "data": DateTime.now().toIso8601String()
    });

    await conn.post(
      "/entrega/acareacao-distante-do-local-info",
      body: {
        "IDOS": atividade.idosList,
        "distancia": distanciaAgenteLocal,
        "distanciaMax": distanciaMax,
        "latitudeAgente": position.latitude,
        "longitudeAgente": position.longitude,
        "latitudeentrega": atividade.info?.latitudeentrega,
        "longitudeentrega": atividade.info?.longitudeentrega,
        "latitudecliente": atividade.info?.latitudecliente,
        "longitudecliente": atividade.info?.longitudecliente,
      },
    );

    pausar = true;

    await asuka.Asuka.showDialog(
      barrierColor: Colors.black.withOpacity(0.5),
      builder: (ctx) => AcareacaoDistanteDoLocal(
        position: LatLng(
          position.latitude,
          position.longitude,
        ),
        atividade: atividade,
        distanciaDoAgente: distanciaAgenteLocal.toDouble(),
        distanciaAgenteLocal: distanciaAgenteLocal,
      ),
    );
  }

  return pausar;
}

class AcareacaoDistanteDoLocal extends StatefulWidget {
  final LatLng? position;
  ClienteNew atividade;
  final double distanciaDoAgente;
  final double? distanciaAgenteLocal;

  AcareacaoDistanteDoLocal({
    super.key,
    this.position,
    required this.atividade,
    required this.distanciaDoAgente,
    this.distanciaAgenteLocal,
  });

  @override
  State<AcareacaoDistanteDoLocal> createState() =>
      _AcareacaoDistanteDoLocalState();
}

class _AcareacaoDistanteDoLocalState extends State<AcareacaoDistanteDoLocal> {
  LocalidadeDoAgente? localAgente;

  @override
  void initState() {
    super.initState();
  }

  Future _getLocalAgente() async {
    try {
      final conn = WebConnector();

      final response =
          await conn.get('/atividades/buscar-localizacao-do-agente');

      final local = LocalidadeDoAgente.fromJson(jsonDecode(response.data));

      localAgente = local;

      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 20,
          vertical: 20,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            GestureDetector(
              onTap: () {
                _getLocalAgente();
              },
              child: const Text(
                "IDENTIFICAÇÃO DE LOCALIZAÇÃO",
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 10),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                MapDistanciaDoLocalAcareacao(
                  positionAgente: LatLng(
                    widget.position!.latitude,
                    widget.position!.longitude,
                  ),
                  positionCliente: LatLng(
                    widget.atividade.info?.latitudecliente ?? 0,
                    widget.atividade.info?.longitudecliente ?? 0,
                  ),
                ),
                const Padding(
                  padding: EdgeInsets.all(10.0),
                  child: LinearProgressIndicator(
                    value: 0.3,
                    backgroundColor: Colors.grey,
                    valueColor: AlwaysStoppedAnimation<Color>(
                        ColorsCustom.customOrange),
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Image.asset('assets/images/problem.png', width: 80),
                    const SizedBox(width: 10),
                    Expanded(
                      child: Text(
                        'Para confirmar a Acareação é necessário que esteja no local da entrega.',
                        style: GoogleFonts.roboto(
                          fontWeight: FontWeight.w700,
                          fontSize: 16,
                          color: ColorsCustom.customOrange,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            FutureBuilder(
                future: _getLocalAgente(),
                initialData: null,
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const LoadingLs();
                  }
                  if (snapshot.hasError) {
                    return const Text("Erro ao buscar localização");
                  }
                  return FcmDeslocamentoWidget(
                    local: localAgente?.endereco ?? "Local não identificado",
                    endereco: widget.atividade.info?.enderecocliente ??
                        "Endereço não informado",
                  );
                }),
            const SizedBox(height: 10),
            Builder(builder: (context) {
              final distanciaKm = widget.distanciaDoAgente / 1000;
              return FcmDistanceData(
                horaFinal: null,
                destino: LatLng(0, 0),
                distanceText:
                    'Distância: ${(distanciaKm).toStringAsFixed(2)} KM',
              );
            }),
          ],
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: ElevatedLsButton(
        text: 'VOLTAR',
        isLoading: false,
        onPressed: () {
          Navigator.of(context).pop();
        },
      ),
    );
  }
}

class MapDistanciaDoLocalAcareacao extends StatefulWidget {
  final LatLng? positionAgente;
  final LatLng? positionCliente;
  const MapDistanciaDoLocalAcareacao(
      {super.key, this.positionAgente, this.positionCliente});

  @override
  State<MapDistanciaDoLocalAcareacao> createState() =>
      _MapDistanciaDoLocalAcareacaoState();
}

class _MapDistanciaDoLocalAcareacaoState
    extends State<MapDistanciaDoLocalAcareacao> {
  final controller = HomeController.instance;
  final mapaPageStorei = MapaPageStore();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<MapaPageState>(
      valueListenable: mapaPageStorei.state,
      builder: (BuildContext context, MapaPageState value, Widget? child) {
        final loc = value.latLng;
        return SizedBox(
          height: 300,
          width: MediaQuery.of(context).size.width,
          child: FlutterMap(
            mapController: value.mapaPageController,
            options: MapOptions(
              rotation: 0,
              center: loc,
              zoom: 13.2,
            ),
            nonRotatedChildren: const [
              Positioned(
                bottom: 5,
                right: 5,
                child: Text(
                  "Octalog",
                ),
              ),
            ],
            children: [
              TileLayer(
                urlTemplate:
                    "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
                subdomains: const ['a', 'b', 'c'],
              ),
              PolylineLayer(
                polylines: [
                  Polyline(
                    points: [
                      LatLng(
                        widget.positionAgente!.latitude,
                        widget.positionAgente!.longitude,
                      ),
                      LatLng(
                        widget.positionCliente!.latitude,
                        widget.positionCliente!.longitude,
                      ),
                    ],
                    strokeWidth: 4.0,
                    color: Colors.blue,
                  ),
                ],
              ),
              MarkerLayer(
                markers: [
                  Marker(
                    width: 20.0,
                    height: 20.0,
                    point: loc,
                    builder: (ctx) {
                      return Image.asset(
                        'assets/images/map_marker.png',
                        color: ColorsCustom.customOrange,
                      );
                    },
                  ),
                  Marker(
                    width: 45.0,
                    height: 45.0,
                    point: widget.positionCliente!,
                    builder: (ctx) {
                      return Container(
                        decoration: BoxDecoration(
                          color: Colors.orange.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(100),
                        ),
                      );
                    },
                  ),
                  Marker(
                    width: 25.0,
                    height: 25.0,
                    point: widget.positionCliente!,
                    builder: (ctx) {
                      return Image.asset(
                        'assets/images/packageLs.png',
                        width: 2,
                        height: 2,
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}

class LocalidadeDoAgente {
  final String? road;
  final String? suburb;
  final String? city;
  final String? postcode;

  LocalidadeDoAgente({
    this.road,
    this.suburb,
    this.city,
    this.postcode,
  });

  factory LocalidadeDoAgente.fromJson(Map<String, dynamic> json) {
    final c = MapFields.load(json['address']);

    return LocalidadeDoAgente(
      road: c.getString('road', 'Rua não encontrada'),
      suburb: c.getString('suburb', ''),
      city: c.getString('city', ''),
      postcode: c.getString('postcode', ''),
    );
  }

  String get endereco {
    if (road == null) {
      return "Endereço não identificado";
    }
    return "$road - $suburb - $city - $postcode";
  }
}
