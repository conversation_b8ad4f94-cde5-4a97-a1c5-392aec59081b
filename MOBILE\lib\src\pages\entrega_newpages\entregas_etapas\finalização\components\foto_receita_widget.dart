import 'dart:io';

import 'package:camera_camera/camera_camera.dart';
import 'package:flutter/material.dart';

import '../../../../../components/flavor_image/flavor_image.dart';
import '../../../../../helpers/web_connector.dart';
import '../../../../../models_new/cliente_new.dart';
import '../../../../../utils/theme_colors.dart';
import '../../../controller/entrega_new_store.dart';
import 'preview_fotos.dart';

class FotoReceitaWidget extends StatefulWidget {
  final EntregaNewStore store;
  final List<int>? bytesReceita;

  final ClienteNew? clienteEscolhido;
  const FotoReceitaWidget({
    super.key,
    required this.store,
    this.bytesReceita,
    this.clienteEscolhido,
  });

  @override
  State<FotoReceitaWidget> createState() => _FotoReceitaWidgetState();
}

class _FotoReceitaWidgetState extends State<FotoReceitaWidget> {
  late XFile? fotopath;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  GestureDetector(
                    onTap: () {
                      widget.store.clearFoto();
                      fotopath = null;
                    },
                    child: Icon(
                      Icons.delete,
                      color: context.customRed,
                      size: 25,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 10),
              SizedBox(
                height: 75,
                // width: double.infinity,
                // child: Card(
                //   margin: EdgeInsets.zero,
                  child: GestureDetector(
                    onTap: () async {
                      final foto = await WebConnector().tirarFoto(context);
                      if (foto == null) return;
                      fotopath = foto;

                      bool? liberarFoto = await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => PreviewImageSemRosto(
                            imageFile: foto,
                          ),
                        ),
                      );
                      if (liberarFoto == null || !liberarFoto) return;

                      widget.store.uploaFotoReceita(foto);
                    },
                    child: Builder(
                      builder: (context) {
                        final foto = widget.bytesReceita;
                        if (foto == null) {
                          return Stack(
                            children: [
                              SizedBox(
                                height: 75,
                                //width: double.infinity,
                                child: FlavorImage(
                                  assetName: 'foto_receita.gif',
                                  fit: BoxFit.fill,
                                ),
                              ),
                              // Opacity(
                              //   opacity: 0.3,
                              //   child: Container(
                              //     height: 200,
                              //     width: double.infinity,
                              //     color: ColorsCustom.customGrey,
                              //   ),
                              // ),
                            ],
                          );
                        }
                        return SizedBox(
                          height: 200,
                          width: double.infinity,
                          child: Image.file(
                            File(fotopath!.path),
                            fit: BoxFit.cover,
                          ),
                        );
                      },
                    ),
                  ),
             //   ),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ],
    );
  }
}
