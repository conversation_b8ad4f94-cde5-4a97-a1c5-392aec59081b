// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:octalog/src/components/buttom_ls/button_ls_custom.dart';

import '../../../database/log_database/log_database.dart';
import '../../../pages/home/<USER>';
import '../../../pages/home/<USER>';
import '../../../utils/colors.dart';
import '../../loading_ls/loading_ls.dart';
import '../../uberizado_single/uberizado_loja_card.dart';
import '../components/card_pedido.dart';
import '../components/fcm_appbar_custom.dart';
import '../fcm_alert_dialog_store.dart';
import '../models/fcm_alert_dados.dart';
import '../models/fcm_deslocamento_get.dart';

class FcmAlertDailogColetaNova extends StatefulWidget {
  final FcmAlertDialogStore store;
  const FcmAlertDailogColetaNova({super.key, required this.store});

  @override
  State<FcmAlertDailogColetaNova> createState() =>
      _FcmAlertDailogColetaNovaState();
}

class _FcmAlertDailogColetaNovaState extends State<FcmAlertDailogColetaNova> {
  final TextEditingController _controllerBarr = TextEditingController();

  bool visibleButton = false;
  FcmDeslocamentoGet get fcmAlertDadosPedidos =>
      widget.store.state.value.fcmAlertDados;

  @override
  Widget build(BuildContext context) {
    return KeyboardVisibilityBuilder(builder: (context, isKeyboardVisible) {
      return ValueListenableBuilder(
        valueListenable: widget.store.state,
        builder: (context, state, child) {
          return SafeArea(
            child: Scaffold(
              body: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: FcmAppBarCustom(
                        title: 'Pedidos para coletar',
                        isBack: true,
                        isLoadgin: true,
                        onPressed: () {
                          state.isLoading
                              ? null
                              : widget.store.getDadosPedidos(context);
                        },
                      ),
                    ),
                    UberizadoLojaCard(
                      endereco: fcmAlertDadosPedidos.endereco,
                      foto: fcmAlertDadosPedidos.logo ?? '',
                      iniciais: fcmAlertDadosPedidos.titulo,
                      titulo: fcmAlertDadosPedidos.titulo,
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    if (!fcmAlertDadosPedidos.reverso)
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        child: SizedBox(
                          height: 54,
                          child: Row(
                            children: [
                              Expanded(
                                child: TextField(
                                  style: const TextStyle(
                                    fontSize: 20,
                                    height: 1,
                                  ),
                                  controller: _controllerBarr,
                                  onChanged: ((value) {
                                    widget.store.setFiltro(value);
                                  }),
                                  textInputAction: TextInputAction.search,
                                  decoration: InputDecoration(
                                    hintText:
                                        'Digite pedido, cliente ou endereço',
                                    hintStyle: const TextStyle(
                                      color: Colors.grey,
                                      fontSize: 15,
                                    ),
                                    prefixIcon: Icon(
                                      Icons.search,
                                      color: Colors.grey.shade600,
                                    ),
                                    suffixIcon: SizedBox(
                                      width: 80,
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.end,
                                        children: [
                                          IconButton(
                                            onPressed: () {
                                              widget.store.scanBarcode(context);
                                            },
                                            icon: const Icon(
                                              Icons.qr_code_scanner,
                                              color: Colors.black54,
                                            ),
                                          ),
                                          if (isKeyboardVisible) ...[
                                            Container(
                                              width: 27,
                                              height: 27,
                                              alignment: Alignment.bottomCenter,
                                              child: GestureDetector(
                                                onTap: () {
                                                  FocusScope.of(context)
                                                      .requestFocus(
                                                          FocusNode());
                                                },
                                                child: Image.asset(
                                                  'assets/images/keyboard_close.png',
                                                  width: 25,
                                                  color: Colors.black54,
                                                ),
                                              ),
                                            ),
                                            const SizedBox(width: 5),
                                          ]
                                        ],
                                      ),
                                    ),
                                    filled: true,
                                    fillColor:
                                        const Color.fromRGBO(230, 230, 230, 1),
                                    border: const OutlineInputBorder(
                                      borderSide: BorderSide.none,
                                      borderRadius: BorderRadius.all(
                                        Radius.circular(8),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              if (state.filtro.isNotEmpty) ...[
                                const SizedBox(width: 5),
                                SizedBox(
                                  width: 90,
                                  child: ButtonLsCustom(
                                    text: 'Coletar',
                                    onPressed: () {
                                      widget.store.scannOnChanged(
                                        _controllerBarr.text,
                                      );
                                      _controllerBarr.clear();
                                      widget.store.setFiltro('');
                                      setState(
                                        () {
                                          visibleButton =
                                              _controllerBarr.text.isNotEmpty;
                                        },
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                      ),
                    const SizedBox(
                      height: 5,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        const Expanded(
                          child: Divider(
                            color: ColorsCustom.customOrange,
                            thickness: 2,
                            indent: 20,
                            endIndent: 20,
                          ),
                        ),
                        Text(
                          '${state.codigos.length} Pedidos selecionados',
                          style: const TextStyle(
                            color: ColorsCustom.customOrange,
                            fontWeight: FontWeight.w500,
                            fontSize: 17,
                          ),
                        ),
                        const Expanded(
                          child: Divider(
                            color: ColorsCustom.customOrange,
                            thickness: 2,
                            indent: 20,
                            endIndent: 20,
                          ),
                        ),
                      ],
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: ConstrainedBox(
                        constraints: BoxConstraints(
                          minHeight: MediaQuery.of(context).size.height * 0.4,
                        ),
                        child: state.isLoading
                            ? const Center(
                                child: LoadingLs(),
                              )
                            : (state.filtro.isNotEmpty &&
                                    state.clientesUnicosFiltrados.isEmpty)
                                ? const Center(
                                    child: Text(
                                      'Nenhum pedido encontrado.',
                                      style: TextStyle(
                                        color: Colors.black54,
                                        fontSize: 17,
                                      ),
                                    ),
                                  )
                                : ListView.builder(
                                    shrinkWrap: true,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    itemCount: state.filtro.isNotEmpty
                                        ? state.clientesUnicosFiltrados.length
                                        : state.clientesUnicos.length,
                                    itemBuilder: (context, index) {
                                      FcmPedido fcmPedido = state
                                              .filtro.isNotEmpty
                                          ? state.clientesUnicosFiltrados[index]
                                          : state.clientesUnicos[index];

                                      return Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: CardPedidoCustom(
                                          store: widget.store,
                                          nomeCliente: fcmPedido.cliente,
                                          endereco: fcmPedido.title,
                                        ),
                                      );
                                    },
                                  ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        left: 20,
                        right: 20,
                        bottom: 10,
                      ),
                      child: SizedBox(
                        height: 54,
                        width: double.infinity,
                        child: TextField(
                          style: const TextStyle(
                            fontSize: 20,
                            height: 1,
                          ),
                          onChanged: widget.store.setLiberadoPor,
                          textInputAction: TextInputAction.search,
                          decoration: InputDecoration(
                            hintText: 'Liberado na loja por',
                            hintStyle: const TextStyle(
                              color: Colors.grey,
                              fontSize: 18,
                            ),
                            prefixIcon: Icon(
                              Icons.supervisor_account_sharp,
                              color: Colors.grey.shade600,
                            ),
                            filled: true,
                            fillColor: const Color.fromARGB(255, 238, 238, 238),
                            border: const OutlineInputBorder(
                              borderSide: BorderSide.none,
                              borderRadius: BorderRadius.all(
                                Radius.circular(8),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 10),
                      child: ButtonLsCustom(
                        isLoading: state.isLoadingButton,
                        onPressed: () async {
                          if (widget.store.state.value.liberadoPor.length > 4) {
                            widget.store.setLoadingButton(true);

                            if (await widget.store.verificarDistancia()) {
                              widget.store.setLoadingButton(false);
                              return;
                            }
                            try {
                              await widget.store.finalizeColeta(context);
                              HomeController.instance.fetchAtividades();
                              Navigator.pushAndRemoveUntil(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const Home(
                                    enterAtividade: false,
                                  ),
                                ),
                                (route) => false,
                              );
                            } catch (e) {
                              widget.store.setLoadingButton(false);
                              await LogDatabase.instance.logError(
                                '/deslocamento/pedidoscoletados',
                                '',
                                'finalizeColeta',
                                {
                                  'error': e.toString(),
                                  'error_type': e.runtimeType.toString(),
                                },
                              );
                            }
                          } else {
                            await showDialog(
                              context: context,
                              builder: (ctx) {
                                return AlertDialog(
                                  title: const Text('Atenção'),
                                  content: const Text(
                                      'Informe o nome de quem liberou a mercadoria na loja.'),
                                  actions: [
                                    TextButton(
                                      onPressed: () {
                                        Navigator.pop(ctx);
                                      },
                                      child: const Text(
                                        'Ok',
                                        style: TextStyle(color: Colors.orange),
                                      ),
                                    )
                                  ],
                                );
                              },
                            );
                          }
                        },
                        text: 'FINALIZAR COLETA',
                        colorBackground: ColorsCustom.customOrange,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      );
    });
  }
}
