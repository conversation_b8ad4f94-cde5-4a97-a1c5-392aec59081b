import 'package:map_fields/map_fields.dart';

class ExpedirModel {
  final int? iDLocal;

  final String? nome;
  final double? latitude;
  final double? longitude;

  ExpedirModel({
    this.iDLocal,
    this.nome,
    this.latitude,
    this.longitude,
  });

  factory ExpedirModel.fromJson(Map<String, dynamic> json) {
    final MapFields mapFields = MapFields.load(json);
    return ExpedirModel(
      // iDLocal: json['IDLocal'],
      // nome: json['Nome'],
      // latitude: json['Latitude'],
      // longitude: json['Longitude'],
      iDLocal: mapFields.getIntNullable('IDLocal'),
      nome: mapFields.getStringNullable('Nome'),
      latitude: mapFields.getDoubleNullable('Latitude'),
      longitude: mapFields.getDoubleNullable('Longitude'),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'IDLocal': iDLocal,
      'Nome': nome,
      'Latitude': latitude,
      'Longitude': longitude,
    };
  }
}
