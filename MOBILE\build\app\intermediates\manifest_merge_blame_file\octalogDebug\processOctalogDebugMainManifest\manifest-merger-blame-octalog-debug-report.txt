1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.octalog"
4    android:versionCode="24"
5    android:versionName="1.2.2" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:9:2-64
15-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:9:19-61
16    <!-- Add permissions for location access -->
17    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
17-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:5:5-78
17-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:5:22-76
18    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
18-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:6:5-80
18-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:6:22-78
19    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
19-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:8:2-75
19-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:8:19-73
20    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
20-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:10:2-76
20-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:10:19-73
21    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
21-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:10:2-76
21-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:10:19-73
22    <uses-permission android:name="android.permission.WAKE_LOCK" />
22-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:11:2-65
22-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:11:19-62
23    <uses-permission android:name="android.permission.CAMERA" />
23-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:12:2-62
23-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:12:19-59
24    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
24-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:13:2-72
24-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:13:19-69
25    <uses-permission android:name="android.permission.VIBRATE" />
25-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:14:2-63
25-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:14:19-60
26    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
26-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:15:2-74
26-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:15:19-71
27    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
27-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:16:2-78
27-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:16:19-75
28    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
28-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:17:2-76
28-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:17:19-73
29    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
29-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:18:2-73
29-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:18:19-71
30    <uses-permission android:name="android.permission.RECORD_AUDIO" />
30-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:19:2-68
30-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:19:19-65
31    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
31-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:21:2-76
31-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:21:19-73
32    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
32-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:22:2-77
32-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:22:19-74
33
34    <uses-feature android:name="android.hardware.camera" />
34-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:23:2-57
34-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:23:16-54
35    <uses-feature android:name="android.hardware.camera.autofocus" />
35-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:24:2-67
35-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:24:16-64
36    <!--
37 Required to query activities that can process text, see:
38         https://developer.android.com/training/package-visibility and
39         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
40
41         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
42    -->
43    <queries>
43-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:63:5-68:15
44        <intent>
44-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:64:9-67:18
45            <action android:name="android.intent.action.PROCESS_TEXT" />
45-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:65:13-72
45-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:65:21-70
46
47            <data android:mimeType="text/plain" />
47-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:66:13-50
47-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:66:19-48
48        </intent>
49        <intent>
49-->[:file_picker] C:\projetos\octa.log\MOBILE\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
50            <action android:name="android.intent.action.GET_CONTENT" />
50-->[:file_picker] C:\projetos\octa.log\MOBILE\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
50-->[:file_picker] C:\projetos\octa.log\MOBILE\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
51
52            <data android:mimeType="*/*" />
52-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:66:13-50
52-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:66:19-48
53        </intent>
54    </queries> <!-- Create a unique permission for your app and use it so only your app can receive your OneSignal messages. -->
55    <permission
55-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:10:5-12:47
56        android:name="com.octalog.permission.C2D_MESSAGE"
56-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:11:9-63
57        android:protectionLevel="signature" />
57-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:12:9-44
58
59    <uses-permission android:name="com.octalog.permission.C2D_MESSAGE" /> <!-- c2dm RECEIVE are basic requirements for push messages through Google's FCM -->
59-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:14:5-79
59-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:14:22-76
60    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" /> <!-- START: ShortcutBadger -->
60-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:19:5-82
60-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:19:22-79
61    <!-- Samsung -->
62    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
62-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:31:5-86
62-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:31:22-83
63    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- HTC -->
63-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:32:5-87
63-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:32:22-84
64    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
64-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:33:5-81
64-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:33:22-78
65    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- Sony -->
65-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:34:5-83
65-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:34:22-80
66    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
66-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:35:5-88
66-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:35:22-85
67    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- Apex -->
67-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:36:5-92
67-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:36:22-89
68    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- Solid -->
68-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:37:5-84
68-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:37:22-81
69    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- Huawei -->
69-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:38:5-83
69-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:38:22-80
70    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
70-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:39:5-91
70-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:39:22-88
71    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
71-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:40:5-92
71-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:40:22-89
72    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- ZUK -->
72-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:41:5-93
72-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:41:22-90
73    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- OPPO -->
73-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:42:5-73
73-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:42:22-70
74    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
74-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:43:5-82
74-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:43:22-79
75    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- EvMe -->
75-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:44:5-83
75-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:44:22-80
76    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
76-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:45:5-88
76-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:45:22-85
77    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
77-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:46:5-89
77-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:46:22-86
78
79    <permission
79-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
80        android:name="com.octalog.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
80-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
81        android:protectionLevel="signature" />
81-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
82
83    <uses-permission android:name="com.octalog.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
83-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
83-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
84    <uses-permission
84-->[com.github.davidliu:audioswitch:89582c47c9a04c62f90aa5e57251af4800a62c9a] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\160f0e0b2f19cfe5268e794069515506\transformed\jetified-audioswitch-89582c47c9a04c62f90aa5e57251af4800a62c9a\AndroidManifest.xml:9:5-11:38
85        android:name="android.permission.BLUETOOTH"
85-->[com.github.davidliu:audioswitch:89582c47c9a04c62f90aa5e57251af4800a62c9a] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\160f0e0b2f19cfe5268e794069515506\transformed\jetified-audioswitch-89582c47c9a04c62f90aa5e57251af4800a62c9a\AndroidManifest.xml:10:9-52
86        android:maxSdkVersion="30" />
86-->[com.github.davidliu:audioswitch:89582c47c9a04c62f90aa5e57251af4800a62c9a] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\160f0e0b2f19cfe5268e794069515506\transformed\jetified-audioswitch-89582c47c9a04c62f90aa5e57251af4800a62c9a\AndroidManifest.xml:11:9-35
87
88    <application
89        android:name="android.app.Application"
90        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
90-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
91        android:debuggable="true"
92        android:extractNativeLibs="true"
93        android:icon="@mipmap/launcher_icon"
94        android:label="@string/app_name" >
95        <activity
96            android:name="com.octalog.MainActivity"
97            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
98            android:exported="true"
99            android:hardwareAccelerated="true"
100            android:launchMode="singleTop"
101            android:taskAffinity=""
102            android:theme="@style/LaunchTheme"
103            android:windowSoftInputMode="adjustResize" >
104
105            <!--
106                 Specifies an Android theme to apply to this Activity as soon as
107                 the Android process has started. This theme is visible to the user
108                 while the Flutter UI initializes. After that, this theme continues
109                 to determine the Window background behind the Flutter UI.
110            -->
111            <meta-data
112                android:name="io.flutter.embedding.android.NormalTheme"
113                android:resource="@style/NormalTheme" />
114
115            <intent-filter>
116                <action android:name="android.intent.action.MAIN" />
117
118                <category android:name="android.intent.category.LAUNCHER" />
119            </intent-filter>
120        </activity>
121        <!--
122             Don't delete the meta-data below.
123             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
124        -->
125        <meta-data
126            android:name="flutterEmbedding"
127            android:value="2" />
128
129        <receiver android:name="com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentReceiver" />
129-->[:flutter_activity_recognition] C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-119
129-->[:flutter_activity_recognition] C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:19-116
130
131        <service
131-->[:flutter_activity_recognition] C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:9-14:72
132            android:name="com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService"
132-->[:flutter_activity_recognition] C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-109
133            android:enabled="true"
133-->[:flutter_activity_recognition] C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-35
134            android:exported="false"
134-->[:flutter_activity_recognition] C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-37
135            android:permission="android.permission.BIND_JOB_SERVICE" />
135-->[:flutter_activity_recognition] C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-69
136        <service
136-->[:geolocator_android] C:\projetos\octa.log\MOBILE\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
137            android:name="com.baseflow.geolocator.GeolocatorLocationService"
137-->[:geolocator_android] C:\projetos\octa.log\MOBILE\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
138            android:enabled="true"
138-->[:geolocator_android] C:\projetos\octa.log\MOBILE\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
139            android:exported="false"
139-->[:geolocator_android] C:\projetos\octa.log\MOBILE\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
140            android:foregroundServiceType="location" />
140-->[:geolocator_android] C:\projetos\octa.log\MOBILE\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
141        <service
141-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
142            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
142-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
143            android:exported="false"
143-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
144            android:permission="android.permission.BIND_JOB_SERVICE" />
144-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
145        <service
145-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
146            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
146-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
147            android:exported="false" >
147-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
148            <intent-filter>
148-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
149                <action android:name="com.google.firebase.MESSAGING_EVENT" />
149-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
149-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
150            </intent-filter>
151        </service>
152
153        <receiver
153-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
154            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
154-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
155            android:exported="true"
155-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
156            android:permission="com.google.android.c2dm.permission.SEND" >
156-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
157            <intent-filter>
157-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
158                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
158-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
158-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
159            </intent-filter>
160        </receiver>
161
162        <service
162-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-39:19
163            android:name="com.google.firebase.components.ComponentDiscoveryService"
163-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:18-89
164            android:directBootAware="true"
164-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
165            android:exported="false" >
165-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
166            <meta-data
166-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
167                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
167-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
168                android:value="com.google.firebase.components.ComponentRegistrar" />
168-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
169            <meta-data
169-->[:firebase_remote_config] C:\projetos\octa.log\MOBILE\build\firebase_remote_config\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
170                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firebaseremoteconfig.FlutterFirebaseAppRegistrar"
170-->[:firebase_remote_config] C:\projetos\octa.log\MOBILE\build\firebase_remote_config\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-139
171                android:value="com.google.firebase.components.ComponentRegistrar" />
171-->[:firebase_remote_config] C:\projetos\octa.log\MOBILE\build\firebase_remote_config\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
172            <meta-data
172-->[:firebase_core] C:\projetos\octa.log\MOBILE\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
173                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
173-->[:firebase_core] C:\projetos\octa.log\MOBILE\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
174                android:value="com.google.firebase.components.ComponentRegistrar" />
174-->[:firebase_core] C:\projetos\octa.log\MOBILE\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
175            <meta-data
175-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
176                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
176-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
177                android:value="com.google.firebase.components.ComponentRegistrar" />
177-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
178            <meta-data
178-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
179                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
179-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
180                android:value="com.google.firebase.components.ComponentRegistrar" />
180-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
181            <meta-data
181-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5136e727b68f569bb48cae627b8d5a4b\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:29:13-31:85
182                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.FirebaseRemoteConfigKtxRegistrar"
182-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5136e727b68f569bb48cae627b8d5a4b\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:30:17-128
183                android:value="com.google.firebase.components.ComponentRegistrar" />
183-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5136e727b68f569bb48cae627b8d5a4b\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:31:17-82
184            <meta-data
184-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5136e727b68f569bb48cae627b8d5a4b\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:32:13-34:85
185                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar"
185-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5136e727b68f569bb48cae627b8d5a4b\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:33:17-117
186                android:value="com.google.firebase.components.ComponentRegistrar" />
186-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5136e727b68f569bb48cae627b8d5a4b\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:34:17-82
187            <meta-data
187-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f383d60f6cbf7d58847eaa68fefd0674\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
188                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
188-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f383d60f6cbf7d58847eaa68fefd0674\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
189                android:value="com.google.firebase.components.ComponentRegistrar" />
189-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f383d60f6cbf7d58847eaa68fefd0674\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
190            <meta-data
190-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f383d60f6cbf7d58847eaa68fefd0674\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
191                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
191-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f383d60f6cbf7d58847eaa68fefd0674\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
192                android:value="com.google.firebase.components.ComponentRegistrar" />
192-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f383d60f6cbf7d58847eaa68fefd0674\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
193            <meta-data
193-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bcc8d2985ec1ab09d1e21c43fe50ab02\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
194                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
194-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bcc8d2985ec1ab09d1e21c43fe50ab02\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
195                android:value="com.google.firebase.components.ComponentRegistrar" />
195-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bcc8d2985ec1ab09d1e21c43fe50ab02\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
196            <meta-data
196-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
197                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
197-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
198                android:value="com.google.firebase.components.ComponentRegistrar" />
198-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
199            <meta-data
199-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf6a27b1025e5f977c0f155cbd2c4f7c\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:12:13-14:85
200                android:name="com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar"
200-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf6a27b1025e5f977c0f155cbd2c4f7c\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:13:17-109
201                android:value="com.google.firebase.components.ComponentRegistrar" />
201-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf6a27b1025e5f977c0f155cbd2c4f7c\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:14:17-82
202            <meta-data
202-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e32c06b9e94e0afe422db7a8cc6919d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
203                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
203-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e32c06b9e94e0afe422db7a8cc6919d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
204                android:value="com.google.firebase.components.ComponentRegistrar" />
204-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e32c06b9e94e0afe422db7a8cc6919d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
205        </service>
206
207        <provider
207-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
208            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
208-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
209            android:authorities="com.octalog.flutterfirebasemessaginginitprovider"
209-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
210            android:exported="false"
210-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
211            android:initOrder="99" />
211-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
212        <provider
212-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
213            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
213-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
214            android:authorities="com.octalog.flutter.image_provider"
214-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
215            android:exported="false"
215-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
216            android:grantUriPermissions="true" >
216-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
217            <meta-data
217-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
218                android:name="android.support.FILE_PROVIDER_PATHS"
218-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
219                android:resource="@xml/flutter_image_picker_file_paths" />
219-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
220        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
221        <service
221-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
222            android:name="com.google.android.gms.metadata.ModuleDependencies"
222-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
223            android:enabled="false"
223-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
224            android:exported="false" >
224-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
225            <intent-filter>
225-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
226                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
226-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
226-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
227            </intent-filter>
228
229            <meta-data
229-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
230                android:name="photopicker_activity:0:required"
230-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
231                android:value="" />
231-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
232        </service>
233
234        <activity
234-->[:url_launcher_android] C:\projetos\octa.log\MOBILE\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
235            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
235-->[:url_launcher_android] C:\projetos\octa.log\MOBILE\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
236            android:exported="false"
236-->[:url_launcher_android] C:\projetos\octa.log\MOBILE\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
237            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
237-->[:url_launcher_android] C:\projetos\octa.log\MOBILE\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
238        <!--
239        Service for holding metadata. Cannot be instantiated.
240        Metadata will be merged from other manifests.
241        -->
242        <service
242-->[androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41ce894b414f4e410f366a4a6ad7589\transformed\jetified-camera-core-1.3.4\AndroidManifest.xml:29:9-33:78
243            android:name="androidx.camera.core.impl.MetadataHolderService"
243-->[androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41ce894b414f4e410f366a4a6ad7589\transformed\jetified-camera-core-1.3.4\AndroidManifest.xml:30:13-75
244            android:enabled="false"
244-->[androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41ce894b414f4e410f366a4a6ad7589\transformed\jetified-camera-core-1.3.4\AndroidManifest.xml:31:13-36
245            android:exported="false" >
245-->[androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41ce894b414f4e410f366a4a6ad7589\transformed\jetified-camera-core-1.3.4\AndroidManifest.xml:32:13-37
246            <meta-data
246-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a600f391fc985fb6fe77353ae0360c6c\transformed\jetified-camera-camera2-1.3.4\AndroidManifest.xml:30:13-32:89
247                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
247-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a600f391fc985fb6fe77353ae0360c6c\transformed\jetified-camera-camera2-1.3.4\AndroidManifest.xml:31:17-103
248                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
248-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a600f391fc985fb6fe77353ae0360c6c\transformed\jetified-camera-camera2-1.3.4\AndroidManifest.xml:32:17-86
249        </service>
250        <service
250-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d7c6518f3a5e07ad02d7ba3ce0d5e\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:9:9-15:19
251            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
251-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d7c6518f3a5e07ad02d7ba3ce0d5e\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:10:13-91
252            android:directBootAware="true"
252-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:17:13-43
253            android:exported="false" >
253-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d7c6518f3a5e07ad02d7ba3ce0d5e\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:11:13-37
254            <meta-data
254-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d7c6518f3a5e07ad02d7ba3ce0d5e\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:12:13-14:85
255                android:name="com.google.firebase.components:com.google.mlkit.vision.face.internal.FaceRegistrar"
255-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d7c6518f3a5e07ad02d7ba3ce0d5e\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:13:17-114
256                android:value="com.google.firebase.components.ComponentRegistrar" />
256-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d7c6518f3a5e07ad02d7ba3ce0d5e\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:14:17-82
257            <meta-data
257-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\087b3f5fae32c8257c73d14fa1c12986\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:12:13-14:85
258                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
258-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\087b3f5fae32c8257c73d14fa1c12986\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:13:17-120
259                android:value="com.google.firebase.components.ComponentRegistrar" />
259-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\087b3f5fae32c8257c73d14fa1c12986\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:14:17-82
260            <meta-data
260-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70809b849f79bdfe99712e94237d768f\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
261                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
261-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70809b849f79bdfe99712e94237d768f\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
262                android:value="com.google.firebase.components.ComponentRegistrar" />
262-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70809b849f79bdfe99712e94237d768f\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
263            <meta-data
263-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:20:13-22:85
264                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
264-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:21:17-120
265                android:value="com.google.firebase.components.ComponentRegistrar" />
265-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:22:17-82
266        </service>
267
268        <receiver
268-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:49:9-60:20
269            android:name="com.onesignal.notifications.receivers.FCMBroadcastReceiver"
269-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:50:13-86
270            android:exported="true"
270-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:51:13-36
271            android:permission="com.google.android.c2dm.permission.SEND" >
271-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:52:13-73
272
273            <!-- High priority so OneSignal payloads can be filtered from other FCM receivers -->
274            <intent-filter android:priority="999" >
274-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:55:13-59:29
274-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:55:28-50
275                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
275-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
275-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
276
277                <category android:name="com.octalog" />
277-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:58:17-61
277-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:58:27-58
278            </intent-filter>
279        </receiver>
280
281        <service
281-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:62:9-68:19
282            android:name="com.onesignal.notifications.services.HmsMessageServiceOneSignal"
282-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:63:13-91
283            android:exported="false" >
283-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:64:13-37
284            <intent-filter>
284-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:65:13-67:29
285                <action android:name="com.huawei.push.action.MESSAGING_EVENT" />
285-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:66:17-81
285-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:66:25-78
286            </intent-filter>
287        </service> <!-- CAUTION: OneSignal backend includes the activity name in the payload, modifying the name without sync may result in notification click not firing -->
288        <activity
288-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:69:9-77:20
289            android:name="com.onesignal.NotificationOpenedActivityHMS"
289-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:70:13-71
290            android:exported="true"
290-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:71:13-36
291            android:noHistory="true"
291-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:72:13-37
292            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
292-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:73:13-72
293            <intent-filter>
293-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:74:13-76:29
294                <action android:name="android.intent.action.VIEW" />
294-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:75:17-69
294-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:75:25-66
295            </intent-filter>
296        </activity>
297
298        <receiver
298-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:79:9-81:39
299            android:name="com.onesignal.notifications.receivers.NotificationDismissReceiver"
299-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:80:13-93
300            android:exported="true" />
300-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:81:13-36
301        <receiver
301-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:82:9-89:20
302            android:name="com.onesignal.notifications.receivers.BootUpReceiver"
302-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:83:13-80
303            android:exported="true" >
303-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:84:13-36
304            <intent-filter>
304-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:85:13-88:29
305                <action android:name="android.intent.action.BOOT_COMPLETED" />
305-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:86:17-79
305-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:86:25-76
306                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
306-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:87:17-82
306-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:87:25-79
307            </intent-filter>
308        </receiver>
309        <receiver
309-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:90:9-96:20
310            android:name="com.onesignal.notifications.receivers.UpgradeReceiver"
310-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:91:13-81
311            android:exported="true" >
311-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:92:13-36
312            <intent-filter>
312-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:93:13-95:29
313                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
313-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:94:17-84
313-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:94:25-81
314            </intent-filter>
315        </receiver>
316
317        <activity
317-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:98:9-104:75
318            android:name="com.onesignal.notifications.activities.NotificationOpenedActivity"
318-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:99:13-93
319            android:excludeFromRecents="true"
319-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:100:13-46
320            android:exported="true"
320-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:101:13-36
321            android:noHistory="true"
321-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:102:13-37
322            android:taskAffinity=""
322-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:103:13-36
323            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
323-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:104:13-72
324        <activity
324-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:105:9-110:75
325            android:name="com.onesignal.notifications.activities.NotificationOpenedActivityAndroid22AndOlder"
325-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:106:13-110
326            android:excludeFromRecents="true"
326-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:107:13-46
327            android:exported="true"
327-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:108:13-36
328            android:noHistory="true"
328-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:109:13-37
329            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
329-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:110:13-72
330
331        <receiver
331-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
332            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
332-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
333            android:exported="true"
333-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
334            android:permission="com.google.android.c2dm.permission.SEND" >
334-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
335            <intent-filter>
335-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
336                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
336-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
336-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
337            </intent-filter>
338
339            <meta-data
339-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
340                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
340-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
341                android:value="true" />
341-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
342        </receiver>
343        <!--
344             FirebaseMessagingService performs security checks at runtime,
345             but set to not exported to explicitly avoid allowing another app to call it.
346        -->
347        <service
347-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
348            android:name="com.google.firebase.messaging.FirebaseMessagingService"
348-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
349            android:directBootAware="true"
349-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
350            android:exported="false" >
350-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
351            <intent-filter android:priority="-500" >
351-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
352                <action android:name="com.google.firebase.MESSAGING_EVENT" />
352-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
352-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
353            </intent-filter>
354        </service>
355
356        <provider
356-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:9:9-13:38
357            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
357-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:10:13-78
358            android:authorities="com.octalog.mlkitinitprovider"
358-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:11:13-69
359            android:exported="false"
359-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:12:13-37
360            android:initOrder="99" />
360-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:13:13-35
361
362        <activity
362-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07660f349a2fe95c07a6bc7026689012\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
363            android:name="com.google.android.gms.common.api.GoogleApiActivity"
363-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07660f349a2fe95c07a6bc7026689012\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
364            android:exported="false"
364-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07660f349a2fe95c07a6bc7026689012\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
365            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
365-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07660f349a2fe95c07a6bc7026689012\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
366
367        <provider
367-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
368            android:name="com.google.firebase.provider.FirebaseInitProvider"
368-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
369            android:authorities="com.octalog.firebaseinitprovider"
369-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
370            android:directBootAware="true"
370-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
371            android:exported="false"
371-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
372            android:initOrder="100" />
372-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
373
374        <uses-library
374-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
375            android:name="androidx.window.extensions"
375-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
376            android:required="false" />
376-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
377        <uses-library
377-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
378            android:name="androidx.window.sidecar"
378-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
379            android:required="false" />
379-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
380
381        <service
381-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:13:9-16:72
382            android:name="com.onesignal.core.services.SyncJobService"
382-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:14:13-70
383            android:exported="false"
383-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:15:13-37
384            android:permission="android.permission.BIND_JOB_SERVICE" />
384-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:16:13-69
385
386        <activity
386-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:18:9-21:75
387            android:name="com.onesignal.core.activities.PermissionsActivity"
387-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:19:13-77
388            android:exported="false"
388-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:20:13-37
389            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
389-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:21:13-72
390
391        <provider
391-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
392            android:name="androidx.startup.InitializationProvider"
392-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
393            android:authorities="com.octalog.androidx-startup"
393-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
394            android:exported="false" >
394-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
395            <meta-data
395-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
396                android:name="androidx.work.WorkManagerInitializer"
396-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
397                android:value="androidx.startup" />
397-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
398            <meta-data
398-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95a194f62dbd62c14638e0c38a7881a4\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
399                android:name="androidx.emoji2.text.EmojiCompatInitializer"
399-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95a194f62dbd62c14638e0c38a7881a4\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
400                android:value="androidx.startup" />
400-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95a194f62dbd62c14638e0c38a7881a4\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
401            <meta-data
401-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0095a4dba85ef3c38f0a9652b34cd9be\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
402                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
402-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0095a4dba85ef3c38f0a9652b34cd9be\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
403                android:value="androidx.startup" />
403-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0095a4dba85ef3c38f0a9652b34cd9be\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
404            <meta-data
404-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
405                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
405-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
406                android:value="androidx.startup" />
406-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
407        </provider>
408
409        <service
409-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
410            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
410-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
411            android:directBootAware="false"
411-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
412            android:enabled="@bool/enable_system_alarm_service_default"
412-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
413            android:exported="false" />
413-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
414        <service
414-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
415            android:name="androidx.work.impl.background.systemjob.SystemJobService"
415-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
416            android:directBootAware="false"
416-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
417            android:enabled="@bool/enable_system_job_service_default"
417-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
418            android:exported="true"
418-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
419            android:permission="android.permission.BIND_JOB_SERVICE" />
419-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
420        <service
420-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
421            android:name="androidx.work.impl.foreground.SystemForegroundService"
421-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
422            android:directBootAware="false"
422-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
423            android:enabled="@bool/enable_system_foreground_service_default"
423-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
424            android:exported="false" />
424-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
425
426        <receiver
426-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
427            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
427-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
428            android:directBootAware="false"
428-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
429            android:enabled="true"
429-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
430            android:exported="false" />
430-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
431        <receiver
431-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
432            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
432-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
433            android:directBootAware="false"
433-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
434            android:enabled="false"
434-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
435            android:exported="false" >
435-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
436            <intent-filter>
436-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
437                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
437-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
437-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
438                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
438-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
438-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
439            </intent-filter>
440        </receiver>
441        <receiver
441-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
442            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
442-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
443            android:directBootAware="false"
443-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
444            android:enabled="false"
444-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
445            android:exported="false" >
445-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
446            <intent-filter>
446-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
447                <action android:name="android.intent.action.BATTERY_OKAY" />
447-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
447-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
448                <action android:name="android.intent.action.BATTERY_LOW" />
448-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
448-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
449            </intent-filter>
450        </receiver>
451        <receiver
451-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
452            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
452-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
453            android:directBootAware="false"
453-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
454            android:enabled="false"
454-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
455            android:exported="false" >
455-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
456            <intent-filter>
456-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
457                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
457-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
457-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
458                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
458-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
458-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
459            </intent-filter>
460        </receiver>
461        <receiver
461-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
462            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
462-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
463            android:directBootAware="false"
463-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
464            android:enabled="false"
464-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
465            android:exported="false" >
465-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
466            <intent-filter>
466-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
467                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
467-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
467-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
468            </intent-filter>
469        </receiver>
470        <receiver
470-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
471            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
471-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
472            android:directBootAware="false"
472-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
473            android:enabled="false"
473-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
474            android:exported="false" >
474-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
475            <intent-filter>
475-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
476                <action android:name="android.intent.action.BOOT_COMPLETED" />
476-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:86:17-79
476-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:86:25-76
477                <action android:name="android.intent.action.TIME_SET" />
477-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
477-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
478                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
478-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
478-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
479            </intent-filter>
480        </receiver>
481        <receiver
481-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
482            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
482-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
483            android:directBootAware="false"
483-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
484            android:enabled="@bool/enable_system_alarm_service_default"
484-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
485            android:exported="false" >
485-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
486            <intent-filter>
486-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
487                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
487-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
487-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
488            </intent-filter>
489        </receiver>
490        <receiver
490-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
491            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
491-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
492            android:directBootAware="false"
492-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
493            android:enabled="true"
493-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
494            android:exported="true"
494-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
495            android:permission="android.permission.DUMP" >
495-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
496            <intent-filter>
496-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
497                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
497-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
497-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
498            </intent-filter>
499        </receiver>
500
501        <meta-data
501-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf636ef2f0738047fe8129f320ef339e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
502            android:name="com.google.android.gms.version"
502-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf636ef2f0738047fe8129f320ef339e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
503            android:value="@integer/google_play_services_version" />
503-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf636ef2f0738047fe8129f320ef339e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
504
505        <receiver
505-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
506            android:name="androidx.profileinstaller.ProfileInstallReceiver"
506-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
507            android:directBootAware="false"
507-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
508            android:enabled="true"
508-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
509            android:exported="true"
509-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
510            android:permission="android.permission.DUMP" >
510-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
511            <intent-filter>
511-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
512                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
512-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
512-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
513            </intent-filter>
514            <intent-filter>
514-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
515                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
515-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
515-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
516            </intent-filter>
517            <intent-filter>
517-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
518                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
518-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
518-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
519            </intent-filter>
520            <intent-filter>
520-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
521                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
521-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
521-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
522            </intent-filter>
523        </receiver>
524
525        <service
525-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3680aa234ac342a007dbd86447bc8f64\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
526            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
526-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3680aa234ac342a007dbd86447bc8f64\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
527            android:exported="false" >
527-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3680aa234ac342a007dbd86447bc8f64\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
528            <meta-data
528-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3680aa234ac342a007dbd86447bc8f64\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
529                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
529-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3680aa234ac342a007dbd86447bc8f64\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
530                android:value="cct" />
530-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3680aa234ac342a007dbd86447bc8f64\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
531        </service>
532        <service
532-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\642b350cb6e62dd2a2606cba48493bbb\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
533            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
533-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\642b350cb6e62dd2a2606cba48493bbb\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
534            android:exported="false"
534-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\642b350cb6e62dd2a2606cba48493bbb\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
535            android:permission="android.permission.BIND_JOB_SERVICE" >
535-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\642b350cb6e62dd2a2606cba48493bbb\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
536        </service>
537
538        <receiver
538-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\642b350cb6e62dd2a2606cba48493bbb\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
539            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
539-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\642b350cb6e62dd2a2606cba48493bbb\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
540            android:exported="false" />
540-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\642b350cb6e62dd2a2606cba48493bbb\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
541
542        <service
542-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dca09e878cbb074545c6b4079b0555c5\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
543            android:name="androidx.room.MultiInstanceInvalidationService"
543-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dca09e878cbb074545c6b4079b0555c5\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
544            android:directBootAware="true"
544-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dca09e878cbb074545c6b4079b0555c5\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
545            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
545-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dca09e878cbb074545c6b4079b0555c5\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
546        <activity
546-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2748b27b9a4960040fddad0a76748cc0\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:14:9-18:65
547            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
547-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2748b27b9a4960040fddad0a76748cc0\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:15:13-93
548            android:exported="false"
548-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2748b27b9a4960040fddad0a76748cc0\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:16:13-37
549            android:stateNotNeeded="true"
549-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2748b27b9a4960040fddad0a76748cc0\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:17:13-42
550            android:theme="@style/Theme.PlayCore.Transparent" />
550-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2748b27b9a4960040fddad0a76748cc0\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:18:13-62
551    </application>
552
553</manifest>
