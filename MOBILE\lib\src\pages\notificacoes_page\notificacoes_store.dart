import 'package:flutter/material.dart';
import 'package:octalog/src/pages/notificacoes_page/notificacoes_state.dart';

import '../../helpers/web_connector.dart';
import '../home/<USER>/notificacoes/notificacao.dart';

class NotificacaoStore {
  final ValueNotifier<NotificacoesState> state = ValueNotifier(
    NotificacoesState(notificacoes: [], loading: false, error: false),
  );

  void setLoading(bool loading) {
    state.value = state.value.copyWith(loading: loading);
  }

  void setError(bool error) {
    state.value = state.value.copyWith(error: error);
  }

  Future<void> loadNotificacoes() async {
    setLoading(true);
    try {
      final conn = WebConnector();
      final response = await conn.get('/notificacoes/lista');
      final List<Notificacao> notificacoes = response.data
          .map<Notificacao>((json) => Notificacao.fromMap(json))
          .toList();
      notificacoes.sort((a, b) => b.id.compareTo(a.id));
      state.value = state.value.copyWith(notificacoes: notificacoes);
      setError(false);
    } catch (e) {
      setError(true);
    } finally {
      setLoading(false);
    }
  }
}
