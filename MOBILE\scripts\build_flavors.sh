#!/bin/bash

# Script para build dos flavors do projeto Octalog
# Uso: ./build_flavors.sh [flavor] [build_type]
# Exemplo: ./build_flavors.sh octalog apk
# Exemplo: ./build_flavors.sh arcargo appbundle

FLAVOR=${1:-octalog}
BUILD_TYPE=${2:-apk}

echo "🚀 Iniciando build do flavor: $FLAVOR"
echo "📦 Tipo de build: $BUILD_TYPE"

# Validar flavor
case $FLAVOR in
  octalog|arcargo|connect|rondolog)
    echo "✅ Flavor válido: $FLAVOR"
    ;;
  *)
    echo "❌ Flavor inválido: $FLAVOR"
    echo "Flavors disponíveis: octalog, arcargo, connect, rondolog"
    exit 1
    ;;
esac

# Validar tipo de build
case $BUILD_TYPE in
  apk|appbundle)
    echo "✅ Tipo de build válido: $BUILD_TYPE"
    ;;
  *)
    echo "❌ Tipo de build inválido: $BUILD_TYPE"
    echo "Tipos disponíveis: apk, appbundle"
    exit 1
    ;;
esac

# Limpar build anterior
echo "🧹 Limpando build anterior..."
flutter clean
flutter pub get

# Definir comando de build baseado no tipo
if [ "$BUILD_TYPE" = "apk" ]; then
    BUILD_CMD="flutter build apk --release --flavor $FLAVOR --dart-define=FLAVOR=$FLAVOR"
else
    BUILD_CMD="flutter build appbundle --release --flavor $FLAVOR --dart-define=FLAVOR=$FLAVOR"
fi

echo "🔨 Executando: $BUILD_CMD"
$BUILD_CMD

if [ $? -eq 0 ]; then
    echo "✅ Build concluído com sucesso!"
    
    # Mostrar localização do arquivo gerado
    if [ "$BUILD_TYPE" = "apk" ]; then
        echo "📱 APK gerado em: build/app/outputs/flutter-apk/app-$FLAVOR-release.apk"
    else
        echo "📱 AAB gerado em: build/app/outputs/bundle/${FLAVOR}Release/app-$FLAVOR-release.aab"
    fi
    
    echo ""
    echo "🎯 Informações do build:"
    echo "   Flavor: $FLAVOR"
    echo "   Tipo: $BUILD_TYPE"
    echo "   Modo: release"
    echo ""
    echo "📋 Para instalar o APK:"
    echo "   adb install build/app/outputs/flutter-apk/app-$FLAVOR-release.apk"
    
else
    echo "❌ Erro durante o build!"
    exit 1
fi
