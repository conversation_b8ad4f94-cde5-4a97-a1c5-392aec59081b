// ignore_for_file: prefer_interpolation_to_compose_strings

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../utils/colors.dart';

class TextFieldLsCustom extends StatelessWidget {
  final TextEditingController? controller;
  final bool isError;
  final String labelText;
  final bool obscureText;
  final String obscuringCharacter;
  final Widget? suffixIcon;
  final Widget? preffixIcon;
  final bool disableLine;
  final TextInputAction? textInputAction;
  final TextInputType? keyboardType;
  final int? maxCaracteres;
  final bool isTelefone;
  final bool isCpf;
  final bool autoFocus;
  final Function(String)? onChanged;
  final Function(String?)? onSubmitted;
  final bool isDense;
  final bool enabled;
  const TextFieldLsCustom({
    super.key,
    this.controller,
    required this.isError,
    required this.labelText,
    this.obscureText = false,
    this.obscuringCharacter = '*',
    this.suffixIcon,
    this.preffixIcon,
    this.disableLine = false,
    this.keyboardType,
    this.maxCaracteres,
    this.isTelefone = false,
    this.onChanged,
    this.onSubmitted,
    this.isDense = false,
    this.autoFocus = false,
    this.isCpf = false,
    this.enabled = true,
    this.textInputAction,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      autofocus: autoFocus,
      keyboardType: keyboardType,
      obscureText: obscureText,
      obscuringCharacter: obscuringCharacter,
      controller: controller,
      cursorColor: ColorsCustom.customGrey,
      textInputAction: textInputAction,
      enabled: enabled,
      inputFormatters: [
        if (maxCaracteres != null)
          LengthLimitingTextInputFormatter(maxCaracteres),
        if (isTelefone) TelefoneMask(),
        if (isCpf) CpfInputFormatter(),
      ],
      style: GoogleFonts.roboto(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: ColorsCustom.customBlack,
      ),
      onFieldSubmitted: onSubmitted,
      onChanged: onChanged,
      decoration: _decorationTextField(
        labelText: labelText,
        suffixIcon: suffixIcon,
        isError: isError,
        preffixIcon: preffixIcon,
        disableLine: disableLine,
        isDense: isDense,
      ),
    );
  }
}

InputDecoration _decorationTextField({
  required String labelText,
  Widget? suffixIcon,
  bool isError = false,
  Widget? preffixIcon,
  bool disableLine = false,
  bool isDense = false,
}) {
  return InputDecoration(
    isDense: isDense,
    labelText: labelText,
    suffixIcon: suffixIcon,
    labelStyle: GoogleFonts.roboto(
      fontSize: 16,
      fontWeight: FontWeight.w500,
      color: ColorsCustom.customGrey,
    ),
    border: _borderTextField(isError, disableLine),
    enabledBorder: _borderTextField(isError, disableLine),
    focusedBorder: _borderTextField(isError, disableLine),
    errorBorder: _borderTextField(isError, disableLine),
    focusedErrorBorder: _borderTextField(isError, disableLine),
    disabledBorder: _borderTextField(isError, disableLine),
    prefixIcon: preffixIcon,
  );
}

InputBorder _borderTextField(
  bool isError,
  bool disableLine,
) {
  return UnderlineInputBorder(
    borderSide: BorderSide(
      color: disableLine
          ? Colors.transparent
          : isError
              ? ColorsCustom.customRed
              : ColorsCustom.customGrey,
    ),
  );
}

class TelefoneMask extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    final numeros = newValue.text.replaceAll(RegExp(r'[^\d]'), '');
    String result = '';
    for (var i = 0; i < numeros.length; i++) {
      if (i < 11) {
        if (i == 0) {
          result += '(';
        }
        if (i == 2) {
          result += ') ';
        }
        if (i == (numeros.length > 10 ? 7 : 6)) {
          result += '-';
        }
        result += numeros[i];
      }
    }
    return TextEditingValue(
      text: result,
      selection: TextSelection.collapsed(
        offset: result.length,
      ),
    );
  }
}

class CpfInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    final numeros = newValue.text.replaceAll(RegExp(r'[^\d]'), '');
    String result = '';
    for (var i = 0; i < numeros.length; i++) {
      if (i == 3) {
        result += '.';
      }
      if (i == 6) {
        result += '.';
      }
      if (i == 9) {
        result += '-';
      }

      result += numeros[i];
    }

    return TextEditingValue(
      text: result,
      selection: TextSelection.collapsed(
        offset: result.length,
      ),
    );
  }
}
