import 'dart:convert';

import 'package:hive/hive.dart';

import 'hash_buscar.dart';

abstract class HashBuscarDatabase {
  static final instance = HashBuscarDatabaseImpl();

  Future<void> initHashBuscar();
  Future<void> setLastBusca(HashBuscar dados);
  Future<HashBuscar> getLastBusca();
}

class HashBuscarDatabaseImpl implements HashBuscarDatabase {
  late Box<String> database;
  bool initialized = false;

  @override
  Future<void> initHashBuscar() async {
    try {
      database = await Hive.openBox<String>('hash_buscar_database');
      initialized = true;
    } catch (e) {
      initialized = true;
    }
  }

  @override
  Future<HashBuscar> getLastBusca() async {
    final dadosStr = database.get(0) ?? '{}';
    return HashBuscar.fromMap(
      json.decode(dadosStr),
    );
  }

  @override
  Future<void> setLastBusca(HashBuscar dados) async {
    await database.put(
      0,
      json.encode(dados.toMap()),
    );
  }
}
