class LoginState {
  final bool error;
  final bool loading;
  final bool visiblePassword;
  final bool success;
  final String message;
  LoginState({
    this.error = false,
    this.loading = false,
    this.visiblePassword = false,
    this.success = false,
    this.message = '',
  });

  LoginState copyWith({
    bool? error,
    bool? loading,
    bool? visiblePassword,
    bool? success,
    String? message,
  }) {
    return LoginState(
      error: error ?? this.error,
      loading: loading ?? this.loading,
      visiblePassword: visiblePassword ?? this.visiblePassword,
      success: success ?? this.success,
      message: message ?? this.message,
    );
  }
}
