import 'dart:async';

import 'package:asuka/asuka.dart' as asuka;
// import 'package:countly_flutter/countly_flutter.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:geolocator/geolocator.dart';
import 'package:image_picker/image_picker.dart';
import 'package:latlong2/latlong.dart';
import 'package:logger/logger.dart';
import 'package:octalog/src/database/baixa_fora_do_local/database_fora_do_local.dart';
import 'package:octalog/src/database/config_blob/config_blob_model.dart';
import 'package:octalog/src/databaseNew/endereconew_offline.dart/endereconew_offline.dart';
import 'package:octalog/src/helpers/web_connector.dart';
import 'package:octalog/src/models_new/endereco_new.dart';
import 'package:octalog/src/utils/extesion.dart';

import '../../../database/config_blob/config_database.dart';
import '../../../database/offline_request/offline_request_database.dart';
import '../../../database/offline_request/offline_request_hive.dart';
import '../../../database/status_atividade/status_atividade_database.dart';
import '../../../helpers/gps/gps_contract.dart';
import '../../../models/cliente.dart';
import '../../../models/status_atividades.dart';
import '../../../models_new/cliente_new.dart';
import '../../entregas/external/entrega_external_model_new.dart';
import 'entrega_new_etapa.dart';
import 'entrega_new_state.dart';

class EntregaNewStore {
  EntregaNewStore(EnderecoNew enderecoNew) {
    state =
        ValueNotifier<EntregaNewState>(EntregaNewState.initial(enderecoNew));
    _init();
  }

  EntregaNewStore.fromState(EntregaNewState value) {
    state = ValueNotifier<EntregaNewState>(value);
    _init();
  }
  late final ValueNotifier<EntregaNewState> state;
  final ValueNotifier<String> time = ValueNotifier<String>('00:00');

  MapController? mapController;
  ConfigBlobModelHive? config;
  Timer? timer;

  Future<void> _init() async {
    config = await ConfigDatabase.instance.getConfig();
    await fetchAtividades();
    state.value = state.value.copyWith(loading: true);
    final stateOld = await getStateDb();
    state.value = state.value.copyWith(loading: false, stateOld: stateOld);
  }

  Future timePedidoOld() async {
    var logger = Logger();
    final config = this.config ?? await ConfigDatabase.instance.getConfig();
    if (config.timerDataFisicoAteChegadaLocal == 0) {
      state.value = state.value.setNullTimerDataFisicoChegadaNoLocalContador();
      return;
    }
    if (state.value.atividade.dataFisico == null) {
      state.value = state.value.setNullTimerDataFisicoChegadaNoLocalContador();
      return;
    }
    while (state.value.timerDataFisicoChegadaNoLocalContador != null) {
      await Future.delayed(const Duration(seconds: 1));
      final now = DateTime.now().dataHoraServidor;
      final dataFisico = state.value.atividade.dataFisico;
      if (dataFisico == null) {
        state.value =
            state.value.setNullTimerDataFisicoChegadaNoLocalContador();
        break;
      }

      final diff = now.difference(dataFisico);
      final timeConfig = config.timerDataFisicoAteChegadaLocal;
      final diffRestante = timeConfig * 60 - diff.inSeconds;

      if (diffRestante <= 0) {
        logger.i('Tempo de pedido expirado');
        state.value =
            state.value.setNullTimerDataFisicoChegadaNoLocalContador();
        break;
      }

      final minutos = diffRestante ~/ 60;
      final segundos = diffRestante % 60;
      final time =
          '${minutos.toString().padLeft(2, '0')}:${segundos.toString().padLeft(2, '0')}';
      state.value =
          state.value.copyWith(timerDataFisicoChegadaNoLocalContador: time);
      logger.i('Tempo restante: $time',
          time: DateTime.now().dataHoraServidorFomart);
    }
  }

  Future baixasNoMesmoLocal() async {
    try {
      final gravar = GravarBaixaForaDoLocalDatabase.instance;
      final list = await gravar.getGravarBaixaForaDoLocal();
      int count = list.length;
      GpsHelperContract.instance.currentPosition;
      final latLng =
          await GpsHelperContract.instance.updateAndGetLastPosition();

      Logger().i('Current Count: $count');

      List<int> times = config?.timersProximasBaixasForaDoLocal ?? [];
      for (var element in list) {
        if (element.latitude == 0 || element.longitude == 0) {
          gravar.deletAll();
          Logger().i('Latitude or Longitude is null');
          break;
        }

        final distance = Geolocator.distanceBetween(
          latLng?.latitude ?? element.latitude,
          latLng?.longitude ?? element.latitude,
          element.latitude,
          element.longitude,
        );
        if (config?.distanciaMetrosFotoFaixada == 0 ||
            config?.distanciaMetrosFotoFaixada == null) {
          Logger().i('DistanciaMetrosFotoFaixada is 0');
          break;
        }
        if (distance < config!.distanciaMetrosFotoFaixada!.toDouble()) {
          final timeToSleep =
              times.length > count ? times[count - 1] : times.last;
          Logger().i('Time to Sleep: $timeToSleep');
          startTimer(timeToSleep);
          break;
        }
        if (distance > config!.distanciaMetrosFotoFaixada!.toDouble()) {
          gravar.deletAll();
          break;
        }
      }
    } catch (e) {
      state.value = state.value.setNullTimerDataFisicoChegadaNoLocalContador();

      Logger().e('Erroraa: $e');
    }
  }

  void startTimer(int initialTime) {
    try {
      int currentTime = initialTime * 60;
      timer?.cancel();
      final int minutes = currentTime ~/ 60;
      final int seconds = currentTime % 60;
      final tempo =
          '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
      state.value =
          state.value.copyWith(timerDataFisicoChegadaNoLocalContador: tempo);
      timer = Timer.periodic(
        const Duration(seconds: 1),
        (Timer t) {
          if (currentTime > 0) {
            currentTime--;
            final int minutes = currentTime ~/ 60;
            final int seconds = currentTime % 60;
            final tempo =
                '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';

            state.value = state.value
                .copyWith(timerDataFisicoChegadaNoLocalContador: tempo);
          } else {
            state.value =
                state.value.setNullTimerDataFisicoChegadaNoLocalContador();
            t.cancel();
          }
        },
      );
    } catch (_) {
      state.value = state.value.setNullTimerDataFisicoChegadaNoLocalContador();
    }
  }

  Future<void> setEntreguePara(String value) async {
    state.value = state.value.copyWith(entreguePara: value);
    await salvarBanco();
  }

  void seterroAcareacaoRealizadaSucesso(bool? status) {
    state.value = state.value.copyWith(erroAcareacaoRealizadaSucesso: status);
  }

  void setAssinaturaPreenchida(bool? status) {
    state.value = state.value.copyWith(assinaturaPreenchida: status);
  }

  static Future<EntregaNewState?> getStateDb() async {
    return await EntregaNewDb.instance.getData();
  }

  Future<void> salvarBanco() async {
    await EntregaNewDb.instance.saveData(state.value);
  }

  void setFinalizar(bool status) {
    state.value = state.value.copyWith(foraDoLocal: status);
  }

  Future<void> limparBanco([bool ignore = false]) async {
    if (!ignore) {
      await EntregaNewDb.instance.clearData();
    }
  }

  Future<void> negativaConclusao(
      int idStatusAtividade, String newSituacao) async {
    final atividade = state.value.atividade.withStatus(
      idStatusAtividade,
      newSituacao,
      state.value.restantes,
    );
    await EnderecoNewOfflineDatabase.instance.updateAtividade(atividade);

    await enviarApi(
      idStatusAtividade,
      state.value.restantes
          .map((e) => e.idosList)
          .toList()
          .expand((e) => e)
          .toList(),
    );
    await limparBanco();
  }

  Future<void> clienteNegativa(
    int idStatusAtividade,
    int indexclienteRestante,
    String newSituacao,
  ) async {
    final clienteRestante = state.value.restantes[indexclienteRestante];
    final indexClienteAtividade = state.value.atividade.clientes
        .indexWhere((e) => e.hashCode == clienteRestante.hashCode);
    if (indexClienteAtividade != -1) {
      debugPrint('indexClienteAtividade $indexClienteAtividade');
      final clienteAtividade =
          state.value.atividade.clientes[indexClienteAtividade].withStatus(
        idStatusAtividade,
        newSituacao,
      );
      final clientes = state.value.atividade.clientes.toList();
      clientes[indexClienteAtividade] = clienteAtividade;
      final newAtividade = state.value.atividade.copyWith(clientes: clientes);
      state.value = state.value.copyWith(atividade: newAtividade);
      await EnderecoNewOfflineDatabase.instance.updateAtividade(newAtividade);
    } else {
      try {
        // await Countly.logException(
        //   'Erro cliente não encontrado',
        //   false,
        // );
      } catch (_) {}
    }
    await enviarApi(idStatusAtividade,
        state.value.restantes[indexclienteRestante].idosList);

    final restantes = state.value.restantes;

    restantes.removeAt(indexclienteRestante);

    state.value = state.value.copyWith(
      restantes: restantes,
    );
    if (state.value.etapa != EntregaNewEtapa.inicio) {
      await salvarBanco();
    }
  }

  Future<void> entregarCliente(
    int idStatusAtividade,
    int indexclienteRestante,
    String newSituacao,
  ) async {
    final clienteRestante = state.value.restantes[indexclienteRestante];
    final indexClienteAtividade = state.value.atividade.clientes
        .indexWhere((e) => e.hashCode == clienteRestante.hashCode);
    if (indexClienteAtividade != -1) {
      final clienteAtividade =
          state.value.atividade.clientes[indexClienteAtividade].withStatus(
        idStatusAtividade,
        newSituacao,
      );
      final clientes = state.value.atividade.clientes.toList();
      clientes[indexClienteAtividade] = clienteAtividade;
      final newAtividade = state.value.atividade.copyWith(clientes: clientes);
      state.value = state.value.copyWith(atividade: newAtividade);
      await EnderecoNewOfflineDatabase.instance.updateAtividade(newAtividade);
    } else {
      try {
        // await Countly.logException(
        //   'Erro cliente não encontrado',
        //   false,
        // );
      } catch (_) {}
    }

    await enviarApi(
      idStatusAtividade,
      state.value.restantes[indexclienteRestante].idosList,
      (state.value.entreguePara ?? '').isEmpty
          ? clienteRestante.nomeCliente
          : (state.value.entreguePara ?? ''),
      valorRecebido,
    );

    final restantes = state.value.restantes;

    restantes.removeAt(indexclienteRestante);

    state.value = state.value.copyWith(
      restantes: restantes,
    );
    await salvarBanco();
  }

  Future<void> localdaentrega() async {
    final position =
        await GpsHelperContract.instance.updateAndGetLastPosition();

    // state.value = state.value.copyWith(
    //   inicio: DateTime.now(),
    //   etapa: EntregaNewEtapa.deslocando,
    //   stateOld: null,
    // );
    // await salvarBanco();

    final idos = state.value.restantes
        .map((e) => e.idosList)
        .toList()
        .expand((e) => e)
        .toList();

    if (idos.isEmpty) return;

    final off = OfflineRequest.novo(
      enviado: false,
      body: {
        'DataHora': DateTime.now().dataHoraServidorFomart.toIso8601String(),
        "ForaDoLocal": state.value.foraDoLocal,
        'IDOS': idos,
        'Latitude': position?.latitude,
        'Longitude': position?.longitude,
      },
      endpoint: '/entrega/foradolocaldaentrega',
      fileBytes: null,
      idAtividade: state.value.atividade.idEnderecoCliente,
      fileName: null,
      headers: {},
      method: 'PUT',
      fileLink: null,
    );
    await OfflineRequestDatabase.instance.addData(off);
  }

  Future iniciarDeslocamentoOnline() async {
    final sincronismoRestante =
        await OfflineRequestDatabase.instance.getQtdEnventosRestantes();
    if (sincronismoRestante > 0) {
      return;
    }
    try {
      final conn = WebConnector();
      final position =
          await GpsHelperContract.instance.updateAndGetLastPosition();
      await conn.put(
        '/entrega/iniciodeslocamento',
        body: {
          'DataHora': DateTime.now().dataHoraServidorFomart.toIso8601String(),
          'IDOS': state.value.restantes
              .map((e) => e.idosList)
              .toList()
              .expand((e) => e)
              .toList(),
          'Latitude': position?.latitude,
          'Longitude': position?.longitude,
          'Online': true,
        },
      ).timeout(
        const Duration(seconds: 5),
      );
    } catch (_) {}
  }

  Future<void> iniciarDeslocamento() async {
    final position =
        await GpsHelperContract.instance.updateAndGetLastPosition();
    state.value = state.value.copyWith(
      inicio: DateTime.now().dataHoraServidorFomart,
      etapa: EntregaNewEtapa.deslocando,
      stateOld: null,
    );
    await salvarBanco();
    // await WebConnector().put(
    //   '/entrega/iniciodeslocamento',
    //   body: {
    //     'DataHora': DateTime.now(),
    //     'IDOS': state.value.restantes
    //         .map((e) => e.idosList)
    //         .toList()
    //         .expand((e) => e)
    //         .toList(),
    //     'Latitude': latLng.latitude.toString(),
    //     'Longitude': latLng.longitude.toString(),
    //   },
    // );
    final off = OfflineRequest.novo(
      enviado: false,
      body: {
        'DataHora': DateTime.now().dataHoraServidorFomart.toIso8601String(),
        'IDOS': state.value.restantes
            .map((e) => e.idosList)
            .toList()
            .expand((e) => e)
            .toList(),
        'Latitude': position?.latitude,
        'Longitude': position?.longitude,
        'Online': false,
      },
      endpoint: '/entrega/iniciodeslocamento',
      fileBytes: null,
      idAtividade: state.value.atividade.idEnderecoCliente,
      fileName: null,
      headers: {},
      method: 'PUT',
      fileLink: null,
    );
    await OfflineRequestDatabase.instance.addData(off);
  }

  Future<void> finalizarFoto() async {
    state.value = state.value
        .copyWith(
          etapa: EntregaNewEtapa.finalizar,
        )
        .semFoto()
        .semFotoAcareacao()
        .semFotoRomaneio();

    await salvarBanco();
  }

  Future<void> finalizarDeslocamento(
    EntregaNewEtapa etapa,
    LatLng? position,
  ) async {
    state.value = state.value.copyWith(
      fim: DateTime.now().dataHoraServidorFomart,
      etapa: etapa,
    );
    await salvarBanco();
    final idos = state.value.restantes
        .map((e) => e.idosList)
        .toList()
        .expand((e) => e)
        .toList();

    if (idos.isEmpty) return;

    final off = OfflineRequest.novo(
      enviado: false,
      body: {
        'DataHora': DateTime.now().dataHoraServidorFomart.toIso8601String(),
        'IDOS': idos,
        'Latitude': position?.latitude,
        'Longitude': position?.longitude,
      },
      endpoint: '/entrega/finaldeslocamento',
      fileBytes: null,
      idAtividade: state.value.atividade.idEnderecoCliente,
      fileName: null,
      headers: {},
      method: 'PUT',
      fileLink: null,
    );
    await OfflineRequestDatabase.instance.addData(off);
  }

  void removeRestante(int index) {
    final rts = state.value.restantes;
    rts.removeAt(index);
    state.value = state.value.copyWith(
      restantes: rts,
    );
  }

  Future<void> enviarApi(
    int idStatusAtividade,
    List<int> idosList, [
    String? nomeRecebedor,
    double? valorRecebido,
  ]) async {
    // await EntregaExternalRepository().sendEntrega(
    //   EntregaExternalModel(
    //     latitude: latLng.latitude,
    //     longitude: latLng.longitude,
    //     dataHoraEntrega: DateTime.now(),
    //     dataHoraDeslocamento: state.value.inicio,
    //     dataHoraChegada: state.value.fim,
    //     foto: state.value.urlFoto,
    //     nomeRecebedor: nomeRecebedor,
    //     valorRecebido: valorRecebido,
    //     idStatusAtividade: idStatusAtividade,
    //     idosList: idosList,
    //   ),
    // );
    await GpsHelperContract.instance.updateAndGetLastPosition();
    final latLng = GpsHelperContract.instance.currentPosition;
    final off = OfflineRequest.novo(
      enviado: false,
      body: EntregaExternalModelNew(
        inicio: state.value.inicio,
        chegada: state.value.fim,
        latitude: latLng?.latitude,
        longitude: latLng?.longitude,
        dataHora: DateTime.now().dataHoraServidorFomart,
        foto: state.value.urlFoto,
        nomeRecebedor: nomeRecebedor,
        valorRecebido: valorRecebido,
        idStatusAtividade: idStatusAtividade,
        idosList: idosList,
        inicioLocalizacao: state.value.inicioLocalizacao,
        chegadaLocalizacao: state.value.fimLocalizacao,
      ).toMap(),
      endpoint: '/entrega/entrega',
      fileBytes: null,
      idAtividade: state.value.atividade.idEnderecoCliente,
      fileName: null,
      headers: {},
      method: 'POST',
      fileLink: null,
    );

    await OfflineRequestDatabase.instance.addData(off);
  }

  Future<void> uploaFotoReceita(XFile foto) async {
    await upLoadFoto(foto, 'receita');
  }

  Future<void> uploaFotoRomaneio(XFile foto) async {
    await upLoadFoto(foto, 'canhoto');
  }

  Future<void> uploaFotoAcareacao(XFile foto) async {
    await upLoadFoto(foto, 'acareacao');
    await upLoadFotoFinalizacao(foto, true);
  }

  Future<void> limparFotos() async {
    state.value = state.value.semFoto().semFotoRomaneio().semFotoAcareacao();

    await salvarBanco();
  }

  Future<void> upLoadFoto(XFile foto, [String? tipo]) async {
    if (tipo == 'canhoto') {
      state.value = state.value.copyWith(
        bytesRomaneio: (await foto.readAsBytes()).toList(),
      );
    } else if (tipo == "acareacao") {
      state.value = state.value.copyWith(
        bytesAcareacao: (await foto.readAsBytes()).toList(),
      );
    } else {
      state.value = state.value.copyWith(
        bytes: (await foto.readAsBytes()).toList(),
      );
    }

    await salvarBanco();

    final off = OfflineRequest.novo(
      enviado: false,
      body: {
        "Foto": null,
        "Origem": tipo ?? "faixada",
        "DataHora": DateTime.now().dataHoraServidorFomart.toIso8601String(),
        "IDOS": state.value.restantes
            .map((e) => e.idosList)
            .toList()
            .expand((e) => e)
            .toList(),
      },
      endpoint: '/atividades/fotos',
      fileBytes: await foto.readAsBytes(),
      idAtividade: state.value.atividade.idEnderecoCliente,
      fileName: foto.name,
      headers: {},
      method: 'POST',
      fileLink: null,
    );
    await OfflineRequestDatabase.instance.addData(off);
  }

  Future<void> _upLoadFotoGenerica(
    XFile fotoInicio,
    String origem, [
    bool popUp = false,
  ]) async {
    final off = OfflineRequest.novo(
      enviado: false,
      body: {
        "Foto": null,
        "Origem": origem,
        "DataHora": DateTime.now().dataHoraServidorFomart.toIso8601String(),
        "IDOS": state.value.restantes
            .map((e) => e.idosList)
            .toList()
            .expand((e) => e)
            .toList(),
      },
      endpoint: '/atividades/fotos',
      fileBytes: await fotoInicio.readAsBytes(),
      idAtividade: state.value.atividade.idEnderecoCliente,
      fileName: fotoInicio.name,
      headers: {},
      method: 'POST',
      fileLink: null,
    );
    await OfflineRequestDatabase.instance.addData(off);
    if (popUp) {
      asuka.AsukaSnackbar.success("A foto foi salva com sucesso!").show();
    }
  }

  Future<void> upLoadFotoInicio(
    XFile fotoInicio, [
    bool popUp = false,
  ]) async {
    await _upLoadFotoGenerica(fotoInicio, 'inicio', popUp);
  }

  Future<void> upLoadFotoDeslocamento(
    XFile fotoInicio, [
    bool popUp = false,
  ]) async {
    await _upLoadFotoGenerica(fotoInicio, 'deslocamento', popUp);
  }

  Future<void> upLoadFotoLonge(
    XFile fotoInicio, [
    bool popUp = false,
  ]) async {
    await _upLoadFotoGenerica(fotoInicio, 'deslocamento', popUp);
  }

  Future<void> upLoadFotoFinalizacao(
    XFile fotoInicio, [
    bool popUp = false,
  ]) async {
    await _upLoadFotoGenerica(fotoInicio, 'finalizacao', popUp);
  }

  Future<void> upLoadFotoProblema(
    XFile fotoInicio, [
    bool popUp = false,
  ]) async {
    await _upLoadFotoGenerica(fotoInicio, 'problema', popUp);
  }

  Future<void> upLoadFotoAssinatura(
    XFile fotoInicio, [
    bool popUp = false,
  ]) async {
    await _upLoadFotoGenerica(fotoInicio, 'assinatura', popUp);
  }

  StatusAtividades statusAtividades = StatusAtividades(
    ocorrenciasInicio: [],
    ocorrenciasChegada: [],
    problemasEntregas: [],
  );

  Future<void> fetchAtividades() async {
    try {
      statusAtividades = await StatusAtividadeDatabase.instance.getStatus() ??
          statusAtividades;
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  List<ClienteNew> get clienteEscolhido {
    return state.value.atividade.clientes;
  }

  int statusAtividadesEscolhido = -1;

  String statusAtividadesEscolhidoNome = '';

  void setStatusAtividadesEscolhido(int value, String nome) {
    statusAtividadesEscolhido = value;
    statusAtividadesEscolhidoNome = nome;
  }

  void setClienteIndex(int? value) {
    if (value == null) {
      state.value = state.value.limpaClienteEscolhido();
    } else {
      state.value = state.value.copyWith(indexClienteEscolhido: value);
    }
  }

  List<Cliente> restantes = <Cliente>[];

  Future<void> deleteRestante(Cliente cliente) async {
    restantes.removeWhere((c) => c.telefone == cliente.telefone);
    restantes = restantes;
    setClienteIndex(null);
  }

  Future<void> clearFoto() async {
    state.value = state.value.semFoto();
    await salvarBanco();
  }

  Future<void> clearFotoRomaneio() async {
    state.value = state.value.semFotoRomaneio();
    await salvarBanco();
  }

  Future<void> clearFotoAcareacao() async {
    state.value = state.value.semFotoAcareacao();
    await salvarBanco();
  }

  double valorRecebido = 0.0;
  void setValorRecebido(String value) async {
    final newValue = double.tryParse(
          value.replaceAll(',', '.'),
        ) ??
        valorRecebido;
    if (newValue > config!.maximoValorReceber) return;
    valorRecebido = newValue;
    state.value = state.value.copyWith();
  }

  void reportSingleVolume(int idos, ClienteNew cliente) {
    final restantes = state.value.restantes;
    final index = restantes.indexWhere(
      (c) => c.telefone == cliente.telefone,
    );
    if (index == -1) return;
    final volumes = restantes[index].volumes;
    volumes.removeWhere((v) => v.idos == idos);
    if (volumes.isEmpty) {
      restantes.removeAt(index);
      setClienteIndex(null);
    } else {
      restantes[index] = restantes[index].copyWith(
        volumes: volumes,
      );
    }
    state.value = state.value.copyWith(restantes: restantes);
  }

  Future<void> negativaSingle(
    int idos,
    int indexClienteRestante,
    int idStatusAtividade,
    String newSituacao,
  ) async {
    await enviarApi(
      idStatusAtividade,
      [idos],
    );
    final cliente = state.value.restantes[indexClienteRestante];
    reportSingleVolume(idos, cliente);
    await salvarBanco();
  }

  acareacaoRealizadaSucesso() {
    state.value = state.value.copyWith(
      acareacaoRealizadaSucesso: !state.value.acareacaoRealizadaSucesso,
    );
  }

  List<StatusAtividadesChild>? filtrarStatusAtividadesChild(
      {bool? isOcorrenciaGlobal, int? indexClienteRestante}) {
    final bool deslocando = state.value.etapa == EntregaNewEtapa.deslocando;
    final bool foto = state.value.etapa == EntregaNewEtapa.foto;
    final bool finalizar = state.value.etapa == EntregaNewEtapa.finalizar;
    final ClienteNew? clienteEscolhido = clienteEscolhid(indexClienteRestante);
    List<StatusAtividadesChild> ocorrencias =
        statusAtividades.ocorrenciasInicio;

    if (foto || deslocando) {
      if (clienteEscolhido?.acareacao ?? false) {
        return statusAtividades.ocorrenciasChegada
            .where((element) => element.acareacao)
            .toList();
      }
      return statusAtividades.ocorrenciasChegada;
    }
    if (finalizar) {
      if (clienteEscolhido?.acareacao ?? false) {
        return statusAtividades.problemasEntregas
            .where((element) => element.acareacao)
            .toList();
      }

      return statusAtividades.problemasEntregas;
    }
    if ((isOcorrenciaGlobal ?? false) && clienteEscolhido!.receita) {
      return ocorrencias.where((element) => element.receita).toList();
    }
    if ((isOcorrenciaGlobal ?? false) &&
        clienteEscolhido != null &&
        clienteEscolhido.receita) {
      return ocorrencias.where((element) => element.receita).toList();
    }

    if (clienteEscolhido?.acareacao ?? false) {
      return ocorrencias.where((element) => element.acareacao).toList();
    }
    return ocorrencias;
  }

  ClienteNew? clienteEscolhid(int? indexClienteRestante) {
    if (state.value.restantes.length == 1 &&
        state.value.atividade.clientes.length == 1) {
      return state.value.restantes.first;
    }
    if (state.value.restantes.isEmpty) return null;
    if (indexClienteRestante != null) {
      if (state.value.restantes.length == 1) {
        return state.value.restantes.last;
      }

      return state.value.restantes.elementAt(indexClienteRestante);
    }
    return state.value.restantes.first;
  }

  void dispose() {
    try {
      state.dispose();
    } catch (_) {}
  }
}

extension DateTimeExt on DateTime {
  String diff(DateTime other) {
    final diff = difference(other);
    final hours = diff.inHours;
    final minutes = diff.inMinutes - hours * 60;
    final seconds = diff.inSeconds - hours * 3600 - minutes * 60;
    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  String get apiFormat {
    final ano = year;
    final mes = month.toString().padLeft(2, '0');
    final dia = day.toString().padLeft(2, '0');
    final hora = hour.toString().padLeft(2, '0');
    final minuto = minute.toString().padLeft(2, '0');
    final dataHora = '$ano-$mes-$dia $hora:$minuto';
    return dataHora;
  }
}
