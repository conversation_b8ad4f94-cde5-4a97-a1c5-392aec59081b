import 'dart:io';

import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';

part 'firebase_drift.g.dart';

class FirebaseDrift extends Table {
  IntColumn get id => integer().unique().autoIncrement()();
  TextColumn get texto => text()();
}

@DriftDatabase(tables: [FirebaseDrift])
class FirebaseDatabaseDrift extends _$FirebaseDatabaseDrift {
  static final FirebaseDatabaseDrift instance = FirebaseDatabaseDrift();

  FirebaseDatabaseDrift() : super(_openConnection());
  @override
  int get schemaVersion => 1;
}

LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(
      p.join(dbFolder.path, 'firebase_db.sqlite'),
    );
    return NativeDatabase(file);
  });
}
