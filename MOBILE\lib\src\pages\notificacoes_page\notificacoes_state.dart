// ignore_for_file: public_member_api_docs, sort_constructors_first
import '../home/<USER>/notificacoes/notificacao.dart';

class NotificacoesState {
  final List<Notificacao> notificacoes;
  final bool loading;
  final bool error;

  NotificacoesState({
    required this.notificacoes,
    required this.loading,
    required this.error,
  });

  NotificacoesState copyWith({
    List<Notificacao>? notificacoes,
    bool? loading,
    bool? error,
  }) {
    return NotificacoesState(
      notificacoes: notificacoes ?? this.notificacoes,
      loading: loading ?? this.loading,
      error: error ?? this.error,
    );
  }
}
