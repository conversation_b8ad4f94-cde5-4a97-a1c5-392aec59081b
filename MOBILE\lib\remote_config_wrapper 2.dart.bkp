import 'dart:async';

import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:octalog/src/helpers/api_ls.dart';

class RemoteConfigWrapper {
  RemoteConfigWrapper._();

  static RemoteConfigWrapper i = RemoteConfigWrapper._();
  final ValueNotifier<String?> urlApiEntregadores = ValueNotifier(null);
  final ValueNotifier<String?> urlApiEntregadoresQA = ValueNotifier(null);

  String get urlApi {
    if (ApiLs.instance.development) {
      return urlApiEntregadoresQA.value ?? ApiLs.instance.api;
    }
    return urlApiEntregadores.value ?? ApiLs.instance.api;
  }

  late final StreamSubscription? subscription;

  Future<void> init({
    required FirebaseRemoteConfig remoteConfig,
  }) async {
    remoteConfig.setConfigSettings(RemoteConfigSettings(
      fetchTimeout: const Duration(seconds: 10),
      minimumFetchInterval: const Duration(hours: 1),
    ));
    remoteConfig.setDefaults(<String, dynamic>{
      'urlApiEntregadores': 'https://apimobile.octalog.com.br',
      'urlApiEntregadoresQA':
          'https://api-entregadores-qa-linux.azurewebsites.net',
    });
    await remoteConfig.fetchAndActivate();
    urlApiEntregadores.value = remoteConfig.getString('urlApiEntregadores');
    urlApiEntregadoresQA.value = remoteConfig.getString('urlApiEntregadoresQA');
    subscription = remoteConfig.onConfigUpdated.listen((snapshot) async {
      await remoteConfig.fetchAndActivate();
      urlApiEntregadores.value = remoteConfig.getString('urlApiEntregadores');
      urlApiEntregadoresQA.value =
          remoteConfig.getString('urlApiEntregadoresQA');
    });
  }
}
