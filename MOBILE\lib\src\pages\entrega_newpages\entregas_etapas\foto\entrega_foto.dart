import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/components/mapa_widget/map_widget.dart';
import 'package:octalog/src/database/baixa_fora_do_local/database_fora_do_local.dart';
import 'package:octalog/src/helpers/web_connector.dart';
import 'package:octalog/src/pages/entrega_newpages/componentes/card_endereco.dart';
import 'package:octalog/src/pages/entrega_newpages/controller/entrega_new_etapa.dart';
import 'package:octalog/src/utils/chamada.dart';

import '../../../../components/buttom_ls/button_ls_custom.dart';
import '../../../../components/custom_scaffold/custom_scaffold.dart';
import '../../../../database/log_database/log_database.dart';
import '../../../../helpers/gps/gps_contract.dart';
import '../../../../utils/colors.dart';
import '../../../home/<USER>';
import '../../componentes/show_dialog_local_entrega.dart';
import '../../controller/entrega_new_state.dart';
import '../../controller/entrega_new_store.dart';

class EntregaFoto extends StatefulWidget {
  final EntregaNewStore store;

  const EntregaFoto({
    super.key,
    required this.store,
  });

  @override
  State<EntregaFoto> createState() => _EntregaFotoState();
}

class _EntregaFotoState extends State<EntregaFoto> {
  bool isColor = true;
  String urlFoto = '';
  bool isLoading = false;

  @override
  void initState() {
    widget.store.baixasNoMesmoLocal();
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ValueListenableBuilder<EntregaNewState>(
        valueListenable: widget.store.state,
        builder: (BuildContext context, EntregaNewState value, Widget? child) {
          final atividade = value.atividade;

          return CustomScaffold(
            canPop: false,
            isColorIcon: isColor,
            onPopClose: () async {
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => const Home(enterAtividade: false),
                ),
              );
              return true;
            },
            onPop: () {
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => const Home(enterAtividade: false),
                ),
              );
            },
            title: atividade.clientes.last.acareacao
                ? '${atividade.volumesLength} Acareação'
                : '${atividade.volumesLength} ${atividade.volumesLength == 1 ? 'pedido' : 'pedidos'} para entregar',
            child: Column(
              children: [
                const SizedBox(height: 10),
                CardEndereco(
                  store: widget.store,
                  isColor: value.bytes == null,
                ),
                Visibility(
                  visible: value.etapa == EntregaNewEtapa.foto &&
                      atividade.clientes.last.exibirNumeroClienteNaEntrega,
                  child: GestureDetector(
                    onTap: () {
                      showContactOptions(
                          context, atividade.clientes.last.telefone);
                    },
                    child: Column(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            color: ColorsCustom.customOrange,
                            borderRadius: BorderRadius.circular(0),
                            border: Border.all(
                              color: ColorsCustom.customBlue,
                              width: 2,
                            ),
                          ),
                          padding: const EdgeInsets.symmetric(horizontal: 10),
                          child: Row(children: [
                            const Icon(
                              Icons.call_end,
                              size: 13,
                              color: ColorsCustom.customWhite,
                            ),
                            Text(
                              atividade.clientes.last.telefone,
                              style: GoogleFonts.roboto(
                                fontSize: 13,
                                fontWeight: FontWeight.bold,
                                color: ColorsCustom.customWhite,
                              ),
                            ),
                          ]),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const AbsorbPointer(
                            absorbing: true,
                            child: SizedBox(
                              height: 200,
                              width: double.infinity,
                              child: Card(
                                margin: EdgeInsets.zero,
                                child: Center(
                                  child: MapWidget(),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 10),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              GestureDetector(
                                onTap: () {
                                  widget.store.clearFoto();
                                },
                                child: const Icon(
                                  Icons.delete,
                                  color: ColorsCustom.customRed,
                                  size: 25,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 10),
                          SizedBox(
                            height: 200,
                            width: double.infinity,
                            child: Card(
                              margin: EdgeInsets.zero,
                              child: GestureDetector(
                                onTap: () async {
                                  final foto =
                                      await WebConnector().tirarFoto(context);
                                  if (foto == null) return;
                                  widget.store.upLoadFoto(foto);
                                },
                                child: Builder(
                                  builder: (context) {
                                    final foto = value.bytes;
                                    if (foto == null) {
                                      return Stack(
                                        children: [
                                          SizedBox(
                                            height: 200,
                                            width: double.infinity,
                                            child: Image.asset(
                                              'assets/images/foto_fachada.png',
                                              fit: BoxFit.cover,
                                            ),
                                          ),
                                          Opacity(
                                            opacity: 0.5,
                                            child: Container(
                                              height: 200,
                                              width: double.infinity,
                                              color: ColorsCustom.customGrey,
                                            ),
                                          ),
                                          Center(
                                            child: Text(
                                              'TIRE UMA FOTO DA FACHADA',
                                              style: GoogleFonts.roboto(
                                                fontSize: 16,
                                                fontWeight: FontWeight.normal,
                                                color: ColorsCustom.customBlack,
                                              ),
                                            ),
                                          ),
                                        ],
                                      );
                                    }
                                    return SizedBox(
                                      height: 200,
                                      width: double.infinity,
                                      child: Image.memory(
                                        Uint8List.fromList(value.bytes!),
                                        fit: BoxFit.cover,
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 20),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                Builder(
                  builder: (context) {
                    final isFoto = !atividade.foto;
                    final foto = value.bytes;
                    final isTime =
                        value.timerDataFisicoChegadaNoLocalContador != null &&
                            value.timerDataFisicoChegadaNoLocalContador != '';
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 22),
                      child: ButtonLsCustom(
                        message: isFoto
                            ? null
                            : foto == null
                                ? 'Tire uma Foto da Fachada'
                                : null,
                        text: isTime
                            ? value.timerDataFisicoChegadaNoLocalContador ?? ''
                            : 'SALVAR DADOS',
                        isLoading: isLoading ||
                            value.timerDataFisicoChegadaNoLocalContador == '',
                        onPressed: isTime
                            ? null
                            : () async {
                                setState(() => isLoading = true);
                                bool desistir = true;
                                try {
                                  await GpsHelperContract.instance
                                      .updateAndGetLastPosition();
                                } catch (_) {
                                  await LogDatabase.instance.logError(
                                    'erro ao pegar localização',
                                    '',
                                    'Entrega Foto',
                                    {},
                                  );
                                }
                                final position = await GpsHelperContract
                                    .instance
                                    .updateAndGetLastPosition();
                                if (position != null &&
                                    atividade.distance(position) > 1) {
                                  // revisar codigo
                                  await showDialog(
                                    context: context,
                                    barrierDismissible: true,
                                    builder: ((ctx) => ShowDialogLocalEntrega(
                                        store: widget.store)),
                                  ).then((value) {
                                    if (value != null) {
                                      desistir = false;
                                    }
                                  });
                                } else {
                                  desistir = false;
                                }
                                if (!desistir) {
                                  final gravar =
                                      GravarBaixaForaDoLocalDatabase.instance;
                                  await gravar.setGravarBaixaForaDoLocal(
                                    ForaDoLocalModel(
                                      idos: value.atividade.clientes.last
                                          .idosList.first,
                                      dataInclusao: DateTime.now(),
                                      isTimeCompleto: true,
                                      latitude: position?.latitude ?? 0,
                                      longitude: position?.longitude ?? 0,
                                    ),
                                  );
                                  widget.store.localdaentrega();
                                  widget.store.finalizarFoto();
                                }
                                setState(() => isLoading = false);
                              },
                      ),
                    );
                  },
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
