import 'package:flutter/material.dart';

import "../../../utils/colors.dart";
import '../models/fcm_alert_dados.dart';

class CardPedidoPadrao extends StatefulWidget {
  final String nomeCliente;
  final String endereco;
  final String status;
  final List<FcmPedido> pedidos;
  final int coletados;
  const CardPedidoPadrao({
    super.key,
    required this.nomeCliente,
    required this.endereco,
    required this.status,
    required this.pedidos,
    required this.coletados,
  });

  @override
  State<CardPedidoPadrao> createState() => _CardPedidoPadraomState();
}

class _CardPedidoPadraomState extends State<CardPedidoPadrao> {
  bool onExpansion = false;
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      color: ColorsCustom.customBlue,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(
          Radius.circular(0),
        ),
        side: BorderSide(
          width: 2,
          color: ColorsCustom.customOrange,
        ),
      ),
      child: Theme(
        data: ThemeData(
          dividerColor: Colors.transparent,
          colorScheme: ColorScheme.fromSwatch().copyWith(),
        ).copyWith(
          dividerColor: Colors.transparent,
        ),
        child: ExpansionTile(
          onExpansionChanged: (value) {
            setState(() {
              onExpansion = value;
            });
          },
          trailing: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                decoration: BoxDecoration(
                 // color: const Color.fromARGB(255, 253, 203, 178),
                  borderRadius: BorderRadius.circular(5),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 5,
                  vertical: 2,
                ),
                child: Text(
                  widget.coletados > 1
                      ? '${widget.coletados} Pedidos'
                      : '${widget.coletados} Pedido',
                  style: const TextStyle(
                    fontSize: 10,
                    color: Color(0xFF757474),
                    fontWeight: FontWeight.w800,
                  ),
                ),
              ),
              Icon(
                onExpansion ? Icons.expand_less : Icons.expand_more,
                color: ColorsCustom.customOrange,
              )
            ],
          ),
          tilePadding: const EdgeInsets.symmetric(
            horizontal: 15.0,
            vertical: 10.0,
          ),
          title: Text(
            widget.nomeCliente,
            style: const TextStyle(
              fontSize: 16.0,
              fontWeight: FontWeight.w500,
              color: ColorsCustom.customOrange,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 8,
              ),
              Text(
                widget.endereco,
                style: TextStyle(
                  fontSize: 14.0,
                  fontWeight: FontWeight.w500,
                  color: ColorsCustom.customBlack.withOpacity(0.6),
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              widget.status.isNotEmpty
                  ? Text(
                      widget.status,
                      style: TextStyle(
                        fontSize: 14.0,
                        fontWeight: FontWeight.w500,
                        color: ColorsCustom.customRed.withOpacity(0.6),
                      ),
                    )
                  : Container(),
            ],
          ),
          children: <Widget>[
            const Padding(
              padding: EdgeInsets.symmetric(
                horizontal: 15.0,
              ),
              child: SizedBox(
                width: double.infinity,
                child: Divider(
                  color: ColorsCustom.customGrey,
                  thickness: 0.5,
                ),
              ),
            ),
            ...List.generate(
              widget.pedidos.length,
              (index) => Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 15.0,
                  vertical: 3.0,
                ),
                child: Row(
                  children: [
                    const SizedBox(
                      width: 5,
                    ),
                    Text(
                      widget.pedidos[index].os,
                      style: const TextStyle(
                          fontSize: 16.0, fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(
              height: 10,
            )
          ],
        ),
      ),
    );
  }
}
