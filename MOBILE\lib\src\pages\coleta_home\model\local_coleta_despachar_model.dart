import 'package:map_fields/map_fields.dart';

class LocalColetaDespachar {
  final int id;
  final String nome;
  final double latitude;
  final double longitude;

  LocalColetaDespachar({
    required this.id,
    required this.nome,
    required this.latitude,
    required this.longitude,
  });

  factory LocalColetaDespachar.fromMapKlev(Map<String, dynamic> map) {
    final f = MapFields.load(map);
    return LocalColetaDespachar(
      id: f.getInt('IDLocal', 0),
      nome: f.getString('Nome', '...'),
      latitude: f.getDouble('Latitude', 0),
      longitude: f.getDouble('Longitude', 0),
    );
  }
}
