import 'package:flutter/material.dart';
import '../../config/flavor_config.dart';

class FlavorImage extends StatelessWidget {
  final String assetName;
  final double? width;
  final double? height;
  final BoxFit? fit;
  final Color? color;
  final BlendMode? colorBlendMode;

  const FlavorImage({
    super.key,
    required this.assetName,
    this.width,
    this.height,
    this.fit,
    this.color,
    this.colorBlendMode,
  });

  @override
  Widget build(BuildContext context) {
    final config = FlavorConfig.instance;
    final assetPath = config.getAsset(assetName);

    return Image.asset(
      assetPath,
      width: width,
      height: height,
      fit: fit,
      color: color,
      colorBlendMode: colorBlendMode,
      errorBuilder: (context, error, stackTrace) {
        // Fallback para asset genérico se não encontrar o específico do flavor
        return Image.asset(
          'assets/images/$assetName',
          width: width,
          height: height,
          fit: fit,
          color: color,
          colorBlendMode: colorBlendMode,
          errorBuilder: (context, error, stackTrace) {
            // Se nem o fallback funcionar, mostra um ícone de erro
            return Icon(
              Icons.image_not_supported,
              size: width ?? height ?? 50,
              color: Colors.grey,
            );
          },
        );
      },
    );
  }
}

// Widget específico para logos
class FlavorLogo extends StatelessWidget {
  final double width;
  final double height;
  final Color? color;

  const FlavorLogo({
    super.key,
    this.width = 200,
    this.height = 200,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return FlavorImage(
      assetName: 'logo_ls.png',
      width: width,
      height: height,
      color: color,
    );
  }
}

// Widget específico para logos brancos
class FlavorLogoWhite extends StatelessWidget {
  final double width;
  final double height;

  const FlavorLogoWhite({
    super.key,
    this.width = 200,
    this.height = 200,
  });

  @override
  Widget build(BuildContext context) {
    return FlavorImage(
      assetName: 'logo_ls_white.png',
      width: width,
      height: height,
    );
  }
}
