import 'package:hive/hive.dart';

import '../../pages/home/<USER>';

abstract class StatusAgenteDatabase {
  static final instance = StatusAgenteDatabaseImpl();

  Future<void> initStatusAgente();
  Future<void> setStatusAgente(bool status);
  Future<bool> getStatusAgente();
}

class StatusAgenteDatabaseImpl implements StatusAgenteDatabase {
  late Box<bool> database;
  bool initialized = false;

  @override
  Future<void> initStatusAgente() async {
    try {
      database = await Hive.openBox<bool>('status_agente_db');
      initialized = true;
      final iniciado = await getStatusAgente();
      HomeController.instance.setIsOnline(iniciado);
    } catch (e) {
      initialized = true;
    }
  }

  @override
  Future<bool> getStatusAgente() async {
    final status = database.get('status_agente');
    return status ?? false;
  }

  @override
  Future<void> setStatusAgente(bool status) async {
    await database.put('status_agente', status);
  }
}
