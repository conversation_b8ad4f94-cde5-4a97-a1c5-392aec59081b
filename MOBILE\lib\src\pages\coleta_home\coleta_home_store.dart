import 'dart:convert';
import 'dart:developer';

import "package:asuka/asuka.dart" as asuka;
import 'package:flutter/material.dart';
import 'package:map_fields/map_fields.dart';
import 'package:octalog/src/components/loading_ls/loading_ls.dart';

import '../../../errors.dart';
import '../../database/envio_erro/envio_erro.dart';
import '../../database/log_database/log_database.dart';
import '../../helpers/web_connector.dart';
import '../../utils/colors.dart';
import '../../utils/offline_helper.dart';
import '../home/<USER>';

import 'coleta_home_state.dart';
import 'components/show_bottom_sheet_coleta_despachar.dart';
import 'model/local_coleta_despachar_model.dart';
import 'model/pedido_coleta_despachar_model.dart';

class ColetaHomeStore {
  final state = ValueNotifier<ColetaHomeState>(ColetaHomeState(loadinTela: false, loadingButton: false, pedidos: []));

  void addPedido(PedidoColetaDespachar pedido) {
    final pedidos = state.value.pedidos;
    pedidos.add(pedido);
    state.value = state.value.copyWith(pedidos: pedidos);
  }

  void removePedido(int pedido) {
    final pedidos = state.value.pedidos;
    pedidos.removeWhere((element) => element.idos == pedido);
    state.value = state.value.copyWith(pedidos: pedidos);
  }

  void removeAllPedido(String cliente) {
    final pedidos = state.value.pedidos;
    pedidos.removeWhere((element) => element.cliente == cliente);
    state.value = state.value.copyWith(pedidos: pedidos);
  }

  // contar quantos pedidos tem o mesmo cliente
  int countCliente(String cliente) {
    final pedidos = state.value.pedidos;
    int count = 0;
    for (var i = 0; i < pedidos.length; i++) {
      if (pedidos[i].cliente == cliente) {
        count++;
      }
    }
    return count;
  }

  List<PedidoColetaDespachar> getPedidosCliente(String cliente) {
    final pedidos = state.value.pedidos;
    final list = <PedidoColetaDespachar>[];
    for (var i = 0; i < pedidos.length; i++) {
      if (pedidos[i].cliente == cliente) {
        list.add(pedidos[i]);
      }
    }
    HomeController.instance.setListColetaAdd(state.value.pedidos.isNotEmpty);

    return list;
  }

  Future deletePedido(int idos) async {
    try {
      final idExluir = idos;
      final url = '/entrega/excluir?force=true&IDOS=$idExluir';

      await WebConnector().delete(url);
      // widget.store.removePedido(pedido.idos);
      removePedido(idos);
    } catch (e) {
      asuka.Asuka.showDialog(
        builder: (context) {
          return AlertDialog(
            title: const Text('Atenção'),
            content: const Text('Não foi possível deletar a coleta, verifique sua internet.'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('CONFIRMAR', style: TextStyle(color: ColorsCustom.customOrange)),
              ),
            ],
          );
        },
      );
    }
    HomeController.instance.setListColetaAdd(state.value.pedidos.isNotEmpty);
  }

  Future<void> coletar({required String os, int? idLocal, int? qtd, BuildContext? context}) async {
    try {
      state.value = state.value.copyWith(loadinTela: true);

      os = os.replaceAll(' ', '').trim();

      if (os.isEmpty) {
        return;
      }

      final conn = WebConnector();
      const url = '/entrega/despachar';
      final body = <String, dynamic>{'OS': os};
      if (idLocal != null) {
        body['IDLocal'] = idLocal;
      }
      if (qtd != null) {
        body['Volume'] = qtd;
      }

      const LoadingLs();
      final response = await conn.post(url, body: body);
      final f = MapFields.load(response.data);
      final locais = f.getList<Map<String, dynamic>>('locais', []);

      if (locais.isNotEmpty) {
        final locaisDespachar = locais.map((e) => LocalColetaDespachar.fromMapKlev(e)).toList();
        state.value = state.value.copyWith(loadinTela: false);
        await showBottomSheetLocalColetaDespachar(context, locaisDespachar, (int id, int qtd) {
          return coletar(os: os, idLocal: id, qtd: qtd);
        });
      } else {
        final pedido = f.getList<Map<String, dynamic>>('pedido', []);
        final pedidoColetaDespachar = pedido.map((e) => PedidoColetaDespachar.fromMapKlev(e)).toList();
        for (var i = 0; i < pedidoColetaDespachar.length; i++) {
          addPedido(pedidoColetaDespachar[i]);
        }

        HomeController.instance.setListColetaAdd(pedidoColetaDespachar.isNotEmpty);
        state.value = state.value.copyWith(loadinTela: false);
      }
    } on ConnectionError catch (e) {
      state.value = state.value.copyWith(loadinTela: false);

      offlineStore.setOffline(false);

      var mensagem = '';

      try {
        final mensagemJson = jsonDecode(e.response);
        mensagem = mensagemJson['Mensagem'];
      } catch (e) {
        mensagem = "Ocorreu um erro inesperado, tente novamente."; //e.toString();
      }

      await LogDatabase.instance.logError('/entrega/despachar', '', 'Erro ao coletar', {'erro': e.toString()});

      try {
        await enviarErro(erro: true, metodo: "entrega/despachar", jsonBody: {'OS': os, 'IDLocal': idLocal, 'Volume': qtd, 'Mensagem': e});
      } catch (e) {
        log(e.toString());
      }

      log(mensagem.toString());

      await asuka.Asuka.showDialog(
        barrierColor: Colors.black.withOpacity(0.5),
        builder: (context) {
          return AlertDialog(
            title: const Text('Falha ao coletar'),
            content: Text(mensagem),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('CONFIRMAR', style: TextStyle(color: ColorsCustom.customOrange)),
              ),
            ],
          );
        },
      );
    }
  }
}
