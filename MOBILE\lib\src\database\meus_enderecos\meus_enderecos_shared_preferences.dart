import 'package:shared_preferences/shared_preferences.dart';

Future gravarEndereco(int id) async {
  final prefs = await SharedPreferences.getInstance();
  prefs.setInt('idEndereco', id);
}

Future<int> lerEndereco() async {
  final prefs = await SharedPreferences.getInstance();
  final idEndereco = prefs.getInt('idEndereco') ?? 0;
  return idEndereco;
}

Future removerEndereco() async {
  final prefs = await SharedPreferences.getInstance();
  prefs.remove('idEndereco');
}
