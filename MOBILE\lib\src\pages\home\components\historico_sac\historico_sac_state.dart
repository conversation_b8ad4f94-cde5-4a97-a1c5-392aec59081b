import '../../../../models/historico_sac.dart';

class HistoricoSacState {
  final List<HistoricoSacModel> historicoSac;
  final bool loading;

  final String errorMessage;
  final DateTime dataInicial;
  final DateTime dataFinal;

  HistoricoSacState({
    required this.historicoSac,
    required this.loading,
    required this.errorMessage,
    required this.dataInicial,
    required this.dataFinal,
  });

  factory HistoricoSacState.initial() {
    return HistoricoSacState(
      historicoSac: [],
      loading: false,
      errorMessage: '',
      dataInicial: DateTime.now().add(const Duration(days: -1)),
      dataFinal: DateTime.now(),
    );
  }

  HistoricoSacState _copyWith({
    List<HistoricoSacModel>? historicoSac,
    bool? loading,
    String? errorMessage,
    DateTime? dataInicial,
    DateTime? dataFinal,
  }) {
    return HistoricoSacState(
      historicoSac: historicoSac ?? this.historicoSac,
      loading: loading ?? this.loading,
      errorMessage: errorMessage ?? this.errorMessage,
      dataInicial: dataInicial ?? this.dataInicial,
      dataFinal: dataFinal ?? this.dataFinal,
    );
  }

  HistoricoSacState setHistoricoSac(List<HistoricoSacModel> historicoSac) {
    return _copyWith(historicoSac: historicoSac);
  }

  HistoricoSacState setLoading(bool loading) {
    return _copyWith(loading: loading);
  }

  HistoricoSacState setErrorMessage(String errorMessage) {
    return _copyWith(errorMessage: errorMessage);
  }

  HistoricoSacState setDataInicial(DateTime dataInicial) {
    return _copyWith(dataInicial: dataInicial);
  }

  HistoricoSacState setDataFinal(DateTime dataFinal) {
    return _copyWith(dataFinal: dataFinal);
  }
}
