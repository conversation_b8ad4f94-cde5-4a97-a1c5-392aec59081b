// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:octalog/src/models_new/endereco_new.dart';
import 'package:octalog/src/pages/entrega_newpages/entregas_etapas/chegada_deslocamento/entrega_chegada.dart';
import 'package:octalog/src/pages/entrega_newpages/entregas_etapas/finaliza%C3%A7%C3%A3o/entrega_finalizacao.dart';
import 'package:octalog/src/pages/entrega_newpages/entregas_etapas/foto/entrega_foto.dart';
import 'package:octalog/src/pages/entrega_newpages/entregas_etapas/inicio_deslocamento/entrega_inicio.dart';

import 'controller/entrega_new_etapa.dart';
import 'controller/entrega_new_state.dart';
import 'controller/entrega_new_store.dart';

class EntregaNewPage extends StatefulWidget {
  final EnderecoNew atividade;

  final bool isNew;
  final EnderecoNew? atividadeAnterior;
  final EntregaNewState? state;
  final bool withInitAuto;
  const EntregaNewPage({
    super.key,
    required this.atividade,
    this.isNew = false,
    this.atividadeAnterior,
    this.state,
    this.withInitAuto = false,
  });

  @override
  State<EntregaNewPage> createState() => _EntregaNewPageState();
}

class _EntregaNewPageState extends State<EntregaNewPage> {
  late final EntregaNewStore store;

  @override
  void initState() {
    init();
    super.initState();
  }

  init() async {
    if (widget.state == null) {
      store = EntregaNewStore(widget.atividade);
    } else {
      store = EntregaNewStore.fromState(widget.state!);
    }
    if (widget.withInitAuto) {
      store.iniciarDeslocamento();
    }
  }

  @override
  void dispose() {
    store.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ValueListenableBuilder<EntregaNewState>(
        valueListenable: store.state,
        builder: (BuildContext context, EntregaNewState value, Widget? child) {
          final etapa = value.etapa;
          switch (etapa) {
            case EntregaNewEtapa.inicio:
              return EntregaInicio(store: store);
            case EntregaNewEtapa.deslocando:
              return EntregaChegada(store: store);
            case EntregaNewEtapa.foto:
              return EntregaFoto(
                store: store,
              );
            case EntregaNewEtapa.finalizar:
              return EntregaFinalizacao(
                store: store,
              );
          }
        },
      ),
    );
  }
}
