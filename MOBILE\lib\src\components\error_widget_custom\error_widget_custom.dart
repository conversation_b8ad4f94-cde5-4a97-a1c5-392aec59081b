import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../utils/colors.dart';

class ErrorWidgetCustom extends StatelessWidget {
  final String errorMessage;
  final int typeError;
  const ErrorWidgetCustom({
    super.key,
    required this.errorMessage,
    this.typeError = 0,
  });

  @override
  Widget build(BuildContext context) {
    final errorList = errorMessage.split('\n');
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (typeError == 1)
          const Icon(
            Icons.report_problem_outlined,
            color: ColorsCustom.customRed,
            size: 60,
          ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (typeError == 0)
              const Icon(
                Icons.error,
                color: ColorsCustom.customRed,
                size: 20,
              ),
            Text(
              errorList.first,
              textAlign: TextAlign.center,
              style: GoogleFonts.roboto(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: ColorsCustom.customRed,
              ),
            ),
          ],
        ),
        ...[
          for (final error in errorList.skip(1))
            Text(
              error,
              textAlign: TextAlign.center,
              style: GoogleFonts.roboto(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: ColorsCustom.customRed,
              ),
            ),
        ],
      ],
    );
  }
}
