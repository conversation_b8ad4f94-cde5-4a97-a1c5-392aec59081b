import 'package:octalog/src/models/meus_enderecos_model.dart';

import '../../helpers/web_connector.dart';

class DeleteEndereco {
  Future<MeusEnderecosModel> delete(int idAgenteEndereco) async {
    final responseStatus = await WebConnector().delete(
      '/agente/removeendereco?IDAgenteEndereco=$idAgenteEndereco',
    );
    if (responseStatus.statusCode == 200) {
      final deleteMeuId = MeusEnderecosModel.fromJson(responseStatus.data);
      return deleteMeuId;
    } else {
      throw Exception('Failed to load delete');
    }
  }
}
