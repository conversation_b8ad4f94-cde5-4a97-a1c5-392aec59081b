{"logs": [{"outputFile": "com.octalog.app-mergeOctalogDebugResources-69:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e22c70eb4700b3262896da0db5fa32c7\\transformed\\jetified-in-app-messages-5.1.29\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,231", "endColumns": "175,84", "endOffsets": "226,311"}, "to": {"startLines": "449,450", "startColumns": "4,4", "startOffsets": "28718,28894", "endColumns": "175,84", "endOffsets": "28889,28974"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\504a3b9bc759ca6028567aaea36c5498\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "330,346,374,3142,3147", "startColumns": "4,4,4,4,4", "startOffsets": "20079,20833,22308,178473,178643", "endLines": "330,346,374,3146,3150", "endColumns": "56,64,63,24,24", "endOffsets": "20131,20893,22367,178638,178787"}}, {"source": "C:\\projetos\\octa.log\\MOBILE\\build\\app\\generated\\res\\resValues\\octalog\\debug\\values\\gradleResValues.xml", "from": {"startLines": "6", "startColumns": "4", "startOffsets": "167", "endColumns": "65", "endOffsets": "228"}, "to": {"startLines": "411", "startColumns": "4", "startOffsets": "24828", "endColumns": "65", "endOffsets": "24889"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\23aabf00f3a34e0b600bc8ec6a919265\\transformed\\work-runtime-2.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "64,65,66,67", "startColumns": "4,4,4,4", "startOffsets": "2266,2331,2401,2465", "endColumns": "64,69,63,60", "endOffsets": "2326,2396,2460,2521"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\87eca1956ddb86a2a7193da8df59556f\\transformed\\jetified-activity-1.9.3\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "343,371", "startColumns": "4,4", "startOffsets": "20689,22144", "endColumns": "41,59", "endOffsets": "20726,22199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\07660f349a2fe95c07a6bc7026689012\\transformed\\jetified-play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "99,100,101,102,103,104,105,106,419,420,421,422,423,424,425,426,428,429,430,431,432,433,434,435,436,3236,3652", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4776,4866,4946,5036,5126,5206,5287,5367,25405,25510,25691,25816,25923,26103,26226,26342,26612,26800,26905,27086,27211,27386,27534,27597,27659,181186,194724", "endLines": "99,100,101,102,103,104,105,106,419,420,421,422,423,424,425,426,428,429,430,431,432,433,434,435,436,3248,3670", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "4861,4941,5031,5121,5201,5282,5362,5442,25505,25686,25811,25918,26098,26221,26337,26440,26795,26900,27081,27206,27381,27529,27592,27654,27733,181496,195136"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\52f643eeb6fba9363586e00d8d04b656\\transformed\\jetified-location-5.1.29\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,127", "endColumns": "71,93", "endOffsets": "122,216"}, "to": {"startLines": "451,452", "startColumns": "4,4", "startOffsets": "28979,29051", "endColumns": "71,93", "endOffsets": "29046,29140"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cdc1fd5a4f8cb18094ee01b408d70e6e\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "5,28,29,60,61,62,68,69,70,71,72,73,74,77,78,79,80,81,82,83,84,85,86,91,92,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,245,246,250,251,252,253,254,255,256,282,283,284,285,286,287,288,289,325,326,327,328,333,341,342,347,369,375,376,377,378,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,463,468,469,470,471,472,473,481,482,486,490,501,506,512,519,523,527,532,536,540,544,548,552,556,562,566,572,576,582,586,591,595,598,602,608,612,618,622,628,631,635,639,643,647,651,652,653,654,657,660,663,666,670,671,672,673,674,677,679,681,683,688,689,693,699,703,704,706,718,719,723,729,733,734,735,739,766,770,771,775,803,975,1001,1172,1198,1229,1237,1243,1259,1281,1286,1291,1301,1310,1319,1323,1330,1349,1356,1357,1366,1369,1372,1376,1380,1384,1387,1388,1393,1398,1408,1413,1420,1426,1427,1430,1434,1439,1441,1443,1446,1449,1451,1455,1458,1465,1468,1471,1475,1477,1481,1483,1485,1487,1491,1499,1507,1519,1525,1534,1537,1548,1551,1552,1557,1558,1595,1664,1734,1735,1745,1754,1906,1908,1912,1915,1918,1921,1924,1927,1930,1933,1937,1940,1943,1946,1950,1953,1957,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1983,1985,1986,1987,1988,1989,1990,1991,1992,1994,1995,1997,1998,2000,2002,2003,2005,2006,2007,2008,2009,2010,2012,2013,2014,2015,2016,2028,2030,2032,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2045,2046,2048,2049,2050,2051,2052,2053,2054,2056,2060,2072,2073,2074,2075,2076,2077,2081,2082,2083,2084,2086,2088,2090,2092,2094,2095,2096,2097,2099,2101,2103,2104,2105,2106,2107,2108,2109,2110,2111,2112,2113,2114,2117,2118,2119,2120,2122,2124,2125,2127,2128,2130,2132,2134,2135,2136,2137,2138,2139,2140,2141,2142,2143,2144,2145,2147,2148,2149,2150,2152,2153,2154,2155,2156,2158,2160,2162,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2178,2184,2259,2262,2265,2268,2282,2299,2341,2344,2373,2400,2409,2473,2841,2890,2928,3066,3190,3214,3220,3249,3270,3394,3422,3428,3572,3604,3671,3742,3842,3862,3917,3929,3955", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "277,943,988,2035,2076,2131,2526,2590,2660,2721,2796,2872,2949,3187,3272,3354,3430,3506,3583,3661,3767,3873,3952,4281,4338,5447,5521,5596,5661,5727,5787,5848,5920,5993,6060,6128,6187,6246,6305,6364,6423,6477,6531,6584,6638,6692,6746,7001,7075,7154,7227,7301,7372,7444,7516,7589,7646,7704,7777,7851,7925,8000,8072,8145,8215,8286,8346,8407,8476,8545,8615,8689,8765,8829,8906,8982,9059,9124,9193,9270,9345,9414,9482,9559,9625,9686,9783,9848,9917,10016,10087,10146,10204,10261,10320,10384,10455,10527,10599,10671,10743,10810,10878,10946,11005,11068,11132,11222,11313,11373,11439,11506,11572,11642,11706,11759,11826,11887,11954,12067,12125,12188,12253,12318,12393,12466,12538,12582,12629,12675,12724,12785,12846,12907,12969,13033,13097,13161,13226,13289,13349,13410,13476,13535,13595,13657,13728,13788,14657,14743,14993,15083,15170,15258,15340,15423,15513,17238,17290,17348,17393,17459,17523,17580,17637,19814,19871,19919,19968,20223,20593,20640,20898,22069,22372,22436,22498,22558,22879,22953,23023,23101,23155,23225,23310,23358,23404,23465,23528,23594,23658,23729,23792,23857,23921,23982,24043,24095,24168,24242,24311,24386,24460,24534,24675,29851,30212,30290,30380,30468,30564,30654,31236,31325,31572,31853,32519,32804,33197,33674,33896,34118,34394,34621,34851,35081,35311,35541,35768,36187,36413,36838,37068,37496,37715,37998,38206,38337,38564,38990,39215,39642,39863,40288,40408,40684,40985,41309,41600,41914,42051,42182,42287,42529,42696,42900,43108,43379,43491,43603,43708,43825,44039,44185,44325,44411,44759,44847,45093,45511,45760,45842,45940,46597,46697,46949,47373,47628,47722,47811,48048,50072,50314,50416,50669,52825,63506,65022,75717,77245,79002,79628,80048,81309,82574,82830,83066,83613,84107,84712,84910,85490,86858,87233,87351,87889,88046,88242,88515,88771,88941,89082,89146,89511,89878,90554,90818,91156,91509,91603,91789,92095,92357,92482,92609,92848,93059,93178,93371,93548,94003,94184,94306,94565,94678,94865,94967,95074,95203,95478,95986,96482,97359,97653,98223,98372,99104,99276,99360,99696,99788,102172,107403,112774,112836,113414,113998,121945,122058,122287,122447,122599,122770,122936,123105,123272,123435,123678,123848,124021,124192,124466,124665,124870,125200,125284,125380,125476,125574,125674,125776,125878,125980,126082,126184,126284,126380,126492,126621,126744,126875,127006,127104,127218,127312,127452,127586,127682,127794,127894,128010,128106,128218,128318,128458,128594,128758,128888,129046,129196,129337,129481,129616,129728,129878,130006,130134,130270,130402,130532,130662,130774,131672,131818,131962,132100,132166,132256,132332,132436,132526,132628,132736,132844,132944,133024,133116,133214,133324,133376,133454,133560,133652,133756,133866,133988,134151,134718,134798,134898,134988,135098,135188,135429,135523,135629,135721,135821,135933,136047,136163,136279,136373,136487,136599,136701,136821,136943,137025,137129,137249,137375,137473,137567,137655,137767,137883,138005,138117,138292,138408,138494,138586,138698,138822,138889,139015,139083,139211,139355,139483,139552,139647,139762,139875,139974,140083,140194,140305,140406,140511,140611,140741,140832,140955,141049,141161,141247,141351,141447,141535,141653,141757,141861,141987,142075,142183,142283,142373,142483,142567,142669,142753,142807,142871,142977,143063,143173,143257,143661,146277,146395,146510,146590,146951,147537,148941,149019,150363,151724,152112,154955,165193,166856,168527,175340,179641,180392,180654,181501,181880,186158,187012,187241,191849,193189,195141,197541,201665,202409,204540,204880,206191", "endLines": "5,28,29,60,61,62,68,69,70,71,72,73,74,77,78,79,80,81,82,83,84,85,86,91,92,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,245,246,250,251,252,253,254,255,256,282,283,284,285,286,287,288,289,325,326,327,328,333,341,342,347,369,375,376,377,378,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,463,468,469,470,471,472,480,481,485,489,493,505,511,518,522,526,531,535,539,543,547,551,555,561,565,571,575,581,585,590,594,597,601,607,611,617,621,627,630,634,638,642,646,650,651,652,653,656,659,662,665,669,670,671,672,673,676,678,680,682,687,688,692,698,702,703,705,717,718,722,728,732,733,734,738,765,769,770,774,802,974,1000,1171,1197,1228,1236,1242,1258,1280,1285,1290,1300,1309,1318,1322,1329,1348,1355,1356,1365,1368,1371,1375,1379,1383,1386,1387,1392,1397,1407,1412,1419,1425,1426,1429,1433,1438,1440,1442,1445,1448,1450,1454,1457,1464,1467,1470,1474,1476,1480,1482,1484,1486,1490,1498,1506,1518,1524,1533,1536,1547,1550,1551,1556,1557,1562,1663,1733,1734,1744,1753,1754,1907,1911,1914,1917,1920,1923,1926,1929,1932,1936,1939,1942,1945,1949,1952,1956,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1982,1984,1985,1986,1987,1988,1989,1990,1991,1993,1994,1996,1997,1999,2001,2002,2004,2005,2006,2007,2008,2009,2011,2012,2013,2014,2015,2016,2029,2031,2033,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2045,2047,2048,2049,2050,2051,2052,2053,2055,2059,2063,2072,2073,2074,2075,2076,2080,2081,2082,2083,2085,2087,2089,2091,2093,2094,2095,2096,2098,2100,2102,2103,2104,2105,2106,2107,2108,2109,2110,2111,2112,2113,2116,2117,2118,2119,2121,2123,2124,2126,2127,2129,2131,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2143,2144,2146,2147,2148,2149,2151,2152,2153,2154,2155,2157,2159,2161,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2178,2258,2261,2264,2267,2281,2287,2308,2343,2372,2399,2408,2472,2835,2844,2917,2955,3083,3213,3219,3225,3269,3393,3413,3427,3431,3577,3638,3682,3807,3861,3916,3928,3954,3961", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "327,983,1032,2071,2126,2188,2585,2655,2716,2791,2867,2944,3022,3267,3349,3425,3501,3578,3656,3762,3868,3947,4027,4333,4391,5516,5591,5656,5722,5782,5843,5915,5988,6055,6123,6182,6241,6300,6359,6418,6472,6526,6579,6633,6687,6741,6795,7070,7149,7222,7296,7367,7439,7511,7584,7641,7699,7772,7846,7920,7995,8067,8140,8210,8281,8341,8402,8471,8540,8610,8684,8760,8824,8901,8977,9054,9119,9188,9265,9340,9409,9477,9554,9620,9681,9778,9843,9912,10011,10082,10141,10199,10256,10315,10379,10450,10522,10594,10666,10738,10805,10873,10941,11000,11063,11127,11217,11308,11368,11434,11501,11567,11637,11701,11754,11821,11882,11949,12062,12120,12183,12248,12313,12388,12461,12533,12577,12624,12670,12719,12780,12841,12902,12964,13028,13092,13156,13221,13284,13344,13405,13471,13530,13590,13652,13723,13783,13851,14738,14825,15078,15165,15253,15335,15418,15508,15599,17285,17343,17388,17454,17518,17575,17632,17686,19866,19914,19963,20014,20252,20635,20684,20939,22096,22431,22493,22553,22610,22948,23018,23096,23150,23220,23305,23353,23399,23460,23523,23589,23653,23724,23787,23852,23916,23977,24038,24090,24163,24237,24306,24381,24455,24529,24670,24740,29899,30285,30375,30463,30559,30649,31231,31320,31567,31848,32100,32799,33192,33669,33891,34113,34389,34616,34846,35076,35306,35536,35763,36182,36408,36833,37063,37491,37710,37993,38201,38332,38559,38985,39210,39637,39858,40283,40403,40679,40980,41304,41595,41909,42046,42177,42282,42524,42691,42895,43103,43374,43486,43598,43703,43820,44034,44180,44320,44406,44754,44842,45088,45506,45755,45837,45935,46592,46692,46944,47368,47623,47717,47806,48043,50067,50309,50411,50664,52820,63501,65017,75712,77240,78997,79623,80043,81304,82569,82825,83061,83608,84102,84707,84905,85485,86853,87228,87346,87884,88041,88237,88510,88766,88936,89077,89141,89506,89873,90549,90813,91151,91504,91598,91784,92090,92352,92477,92604,92843,93054,93173,93366,93543,93998,94179,94301,94560,94673,94860,94962,95069,95198,95473,95981,96477,97354,97648,98218,98367,99099,99271,99355,99691,99783,100061,107398,112769,112831,113409,113993,114084,122053,122282,122442,122594,122765,122931,123100,123267,123430,123673,123843,124016,124187,124461,124660,124865,125195,125279,125375,125471,125569,125669,125771,125873,125975,126077,126179,126279,126375,126487,126616,126739,126870,127001,127099,127213,127307,127447,127581,127677,127789,127889,128005,128101,128213,128313,128453,128589,128753,128883,129041,129191,129332,129476,129611,129723,129873,130001,130129,130265,130397,130527,130657,130769,130909,131813,131957,132095,132161,132251,132327,132431,132521,132623,132731,132839,132939,133019,133111,133209,133319,133371,133449,133555,133647,133751,133861,133983,134146,134303,134793,134893,134983,135093,135183,135424,135518,135624,135716,135816,135928,136042,136158,136274,136368,136482,136594,136696,136816,136938,137020,137124,137244,137370,137468,137562,137650,137762,137878,138000,138112,138287,138403,138489,138581,138693,138817,138884,139010,139078,139206,139350,139478,139547,139642,139757,139870,139969,140078,140189,140300,140401,140506,140606,140736,140827,140950,141044,141156,141242,141346,141442,141530,141648,141752,141856,141982,142070,142178,142278,142368,142478,142562,142664,142748,142802,142866,142972,143058,143168,143252,143372,146272,146390,146505,146585,146946,147179,148049,149014,150358,151719,152107,154950,165003,165323,168221,169879,175907,180387,180649,180849,181875,186153,186759,187236,187387,192059,194267,195448,200562,202404,204535,204875,206186,206389"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1f37faca982b225960866422b14b0e48\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "372", "startColumns": "4", "startOffsets": "22204", "endColumns": "53", "endOffsets": "22253"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\765cbe31314e92111ddb6ec950f59a6b\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "4,2181,2956,2962", "startColumns": "4,4,4,4", "startOffsets": "216,143516,169884,170095", "endLines": "4,2183,2961,3045", "endColumns": "60,12,24,24", "endOffsets": "272,143656,170090,174606"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1a36ee1e87beff061ef9f2c053c4f171\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "87,88,89,90,233,234,438,440,441,442", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "4032,4090,4156,4219,13856,13927,27776,27901,27968,28047", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "4085,4151,4214,4276,13922,13994,27839,27963,28042,28111"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c4042ec9e8cc7d9cdf61197d11f01375\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "410", "startColumns": "4", "startOffsets": "24745", "endColumns": "82", "endOffsets": "24823"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\73db24a2829d8fb9cf5663cfbb96cfeb\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,6,12,20,31,43,49,55,56,57,58,59,329,2288,2294,3683,3691,3706", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,332,505,724,1097,1411,1599,1786,1839,1899,1951,1996,20019,147184,147379,195453,195735,196349", "endLines": "2,11,19,27,42,48,54,55,56,57,58,59,329,2293,2298,3690,3705,3721", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,500,719,938,1406,1594,1781,1834,1894,1946,1991,2030,20074,147374,147532,195730,196344,196998"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\54b8830a716a856dc674cceb64efb5c6\\transformed\\jetified-android-pdf-viewer-3.2.0-beta.3\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "7", "endColumns": "24", "endOffsets": "380"}, "to": {"startLines": "3598", "startColumns": "4", "startOffsets": "192859", "endLines": "3603", "endColumns": "24", "endOffsets": "193184"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0df03071d3ea5a13ca8a701a85da690b\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "373", "startColumns": "4", "startOffsets": "22258", "endColumns": "49", "endOffsets": "22303"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fec79dfcef552889e3835cc7bab572a\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "63,131,275,276,277,278,279,280,281,338,339,340,380,381,437,439,453,459,465,466,467,1563,1755,1758,1764,1770,1773,1779,1783,1786,1793,1799,1802,1808,1813,1818,1825,1827,1833,1839,1847,1852,1859,1864,1870,1874,1881,1885,1891,1897,1900,1904,1905,2836,2879,3046,3084,3226,3414,3432,3496,3506,3516,3523,3529,3639,3808,3825", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2193,6932,16805,16869,16924,16992,17059,17124,17181,20436,20484,20532,22683,22746,27738,27844,29145,29637,29975,30114,30164,100066,114089,114194,114439,114777,114923,115263,115475,115638,116045,116383,116506,116845,117084,117341,117712,117772,118110,118396,118845,119137,119525,119830,120174,120419,120749,120956,121224,121497,121641,121842,121889,165008,166455,174611,175912,180854,186764,187392,189317,189599,189904,190166,190426,194272,200567,201097", "endLines": "63,131,275,276,277,278,279,280,281,338,339,340,380,381,437,439,453,461,465,466,467,1579,1757,1763,1769,1772,1778,1782,1785,1792,1798,1801,1807,1812,1817,1824,1826,1832,1838,1846,1851,1858,1863,1869,1873,1880,1884,1890,1896,1899,1903,1904,1905,2840,2889,3065,3087,3235,3421,3495,3505,3515,3522,3528,3571,3651,3824,3841", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2261,6996,16864,16919,16987,17054,17119,17176,17233,20479,20527,20588,22741,22804,27771,27896,29184,29772,30109,30159,30207,101499,114189,114434,114772,114918,115258,115470,115633,116040,116378,116501,116840,117079,117336,117707,117767,118105,118391,118840,119132,119520,119825,120169,120414,120744,120951,121219,121492,121636,121837,121884,121940,165188,166851,175335,176056,181181,187007,189312,189594,189899,190161,190421,191844,194719,201092,201660"}}, {"source": "C:\\projetos\\octa.log\\MOBILE\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "1588,1592", "startColumns": "4,4", "startOffsets": "101822,102003", "endLines": "1591,1594", "endColumns": "12,12", "endOffsets": "101998,102167"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7f46bbd13206153774356ca6103aa890\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "30,75,76,93,94,129,130,238,239,240,241,242,243,244,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,335,336,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,382,412,413,414,415,416,417,418,464,2017,2018,2022,2023,2027,2179,2180,2845,2918,3088,3121,3151,3184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1037,3027,3099,4396,4461,6800,6869,14169,14239,14307,14379,14449,14510,14584,15827,15888,15949,16011,16075,16137,16198,16266,16366,16426,16492,16565,16634,16691,16743,17691,17763,17839,17904,17963,18022,18082,18142,18202,18262,18322,18382,18442,18502,18562,18622,18681,18741,18801,18861,18921,18981,19041,19101,19161,19221,19281,19340,19400,19460,19519,19578,19637,19696,19755,20323,20358,20944,20999,21062,21117,21175,21233,21294,21357,21414,21465,21515,21576,21633,21699,21733,21768,22809,24894,24961,25033,25102,25171,25245,25317,29904,130914,131031,131232,131342,131543,143377,143449,165328,168226,176061,177792,178792,179474", "endLines": "30,75,76,93,94,129,130,238,239,240,241,242,243,244,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,335,336,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,382,412,413,414,415,416,417,418,464,2017,2021,2022,2026,2027,2179,2180,2850,2927,3120,3141,3183,3189", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1092,3094,3182,4456,4522,6864,6927,14234,14302,14374,14444,14505,14579,14652,15883,15944,16006,16070,16132,16193,16261,16361,16421,16487,16560,16629,16686,16738,16800,17758,17834,17899,17958,18017,18077,18137,18197,18257,18317,18377,18437,18497,18557,18617,18676,18736,18796,18856,18916,18976,19036,19096,19156,19216,19276,19335,19395,19455,19514,19573,19632,19691,19750,19809,20353,20388,20994,21057,21112,21170,21228,21289,21352,21409,21460,21510,21571,21628,21694,21728,21763,21798,22874,24956,25028,25097,25166,25240,25312,25400,29970,131026,131227,131337,131538,131667,143444,143511,165526,168522,177787,178468,179469,179636"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f80248970efbd3bafd1721bfcda96536\\transformed\\jetified-firebase-messaging-24.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "443", "startColumns": "4", "startOffsets": "28116", "endColumns": "81", "endOffsets": "28193"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32b951ec29728b45cedd679ddbec605b\\transformed\\jetified-notifications-5.1.29\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,136", "endColumns": "80,82", "endOffsets": "131,214"}, "to": {"startLines": "454,455", "startColumns": "4,4", "startOffsets": "29189,29270", "endColumns": "80,82", "endOffsets": "29265,29348"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\31710a1d11dfb3018b4d584b1dcc524f\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "247,248,249,257,258,259,334,3578", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "14830,14889,14937,15604,15679,15755,20257,192064", "endLines": "247,248,249,257,258,259,334,3597", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "14884,14932,14988,15674,15750,15822,20318,192854"}}, {"source": "C:\\projetos\\octa.log\\MOBILE\\build\\app\\generated\\res\\google-services\\octalog\\debug\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,137,241,350,470,575", "endColumns": "81,103,108,119,104,73", "endOffsets": "132,236,345,465,570,644"}, "to": {"startLines": "444,445,446,447,448,462", "startColumns": "4,4,4,4,4,4", "startOffsets": "28198,28280,28384,28493,28613,29777", "endColumns": "81,103,108,119,104,73", "endOffsets": "28275,28379,28488,28608,28713,29846"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2748b27b9a4960040fddad0a76748cc0\\transformed\\jetified-core-common-2.0.4\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "166", "endLines": "11", "endColumns": "8", "endOffsets": "571"}, "to": {"startLines": "2064", "startColumns": "4", "startOffsets": "134308", "endLines": "2071", "endColumns": "8", "endOffsets": "134713"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2c52fddffb4a4858b65e09bc36907dcb\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,35,36,37,38,45,47,50,7", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,107,168,230,292,2179,2238,2295,2349,2763,2827,2953,356", "endLines": "2,3,4,5,6,35,36,37,44,46,49,52,34", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "102,163,225,287,351,2233,2290,2344,2758,2822,2948,3076,2174"}, "to": {"startLines": "3,95,96,97,98,235,236,237,494,1580,1582,1585,2851", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "164,4527,4588,4650,4712,13999,14058,14115,32105,101504,101568,101694,165531", "endLines": "3,95,96,97,98,235,236,237,500,1581,1584,1587,2878", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "211,4583,4645,4707,4771,14053,14110,14164,32514,101563,101689,101817,166450"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f6da13a9846494f8148c639e8691af2b\\transformed\\jetified-appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2309,2325,2331,3722,3738", "startColumns": "4,4,4,4,4", "startOffsets": "148054,148479,148657,197003,197414", "endLines": "2324,2330,2340,3737,3741", "endColumns": "24,24,24,24,24", "endOffsets": "148474,148652,148936,197409,197536"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\952d4bb05156a6bae798ebc3d4f6b56b\\transformed\\jetified-core-5.1.29\\res\\values\\values.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,180,263", "endColumns": "124,82,75", "endOffsets": "175,258,334"}, "to": {"startLines": "456,457,458", "startColumns": "4,4,4", "startOffsets": "29353,29478,29561", "endColumns": "124,82,75", "endOffsets": "29473,29556,29632"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cf636ef2f0738047fe8129f320ef339e\\transformed\\jetified-play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "379,427", "startColumns": "4,4", "startOffsets": "22615,26445", "endColumns": "67,166", "endOffsets": "22678,26607"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\07d5ffcb9c6912ffd610dc84a8004380\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "370", "startColumns": "4", "startOffsets": "22101", "endColumns": "42", "endOffsets": "22139"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5c90255c19f9c683385e871d79ee1ace\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "331,332,337,344,345,364,365,366,367,368", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "20136,20176,20393,20731,20786,21803,21857,21909,21958,22019", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "20171,20218,20431,20781,20828,21852,21904,21953,22014,22064"}}]}]}