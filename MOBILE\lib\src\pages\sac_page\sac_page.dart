import 'package:flutter/material.dart';
import 'package:octalog/src/models_new/cliente_new.dart';
import 'package:octalog/src/models_new/endereco_new.dart';
import 'package:octalog/src/pages/sac_page/enum/sap_page_etapa.dart';
import 'package:octalog/src/pages/sac_page/sac_page.state.dart';
import 'package:octalog/src/pages/sac_page/sac_page_store.dart';
import 'package:octalog/src/pages/sac_page/subtela/aviso_sac_page.dart';
import 'package:octalog/src/pages/sac_page/subtela/chat/chat_sac.dart';
import 'package:octalog/src/pages/sac_page/subtela/espera_sac_page.dart';
import 'package:octalog/src/pages/sac_page/subtela/mensagem_sac_page.dart';

class SacPage extends StatefulWidget {
  final int idStatusAtividade;
  final int idos;
  final int? idSacAtendimento;
  final EnderecoNew? endereco;
  final ClienteNew? clienteEscolhido;
  const SacPage({
    super.key,
    required this.idStatusAtividade,
    required this.idos,
    this.idSacAtendimento,
    this.endereco,
    this.clienteEscolhido,
  });

  @override
  State<SacPage> createState() => _SacPageState();
}

class _SacPageState extends State<SacPage> {
  final store = SacPageStore();

  @override
  void initState() {
    init();
    super.initState();

    store.verificarDistancia(widget.clienteEscolhido!);
  }

  init() {
    if (widget.idSacAtendimento != null) {
      store.abrirFilaEmAndamento(widget.idSacAtendimento ?? 0);
    }
    store.setIDStatusAtividadeIDos(widget.idStatusAtividade, widget.idos);
  }

  @override
  void dispose() {
    super.dispose();
    store.disposeChat();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (store.state.value.etapa == SacPageEtapa.mensagem) {
          return true;
        } else {
          return false;
        }
      },
      child: SafeArea(
        child: Scaffold(
          body: ValueListenableBuilder<SacPageState>(
            valueListenable: store.state,
            builder: (_, SacPageState value, __) {
              final etapa = value.etapa;
              switch (etapa) {
                case SacPageEtapa.mensagem:
                  return MensagemSacPage(
                    store: store,
                  );
                case SacPageEtapa.espera:
                  return SacEsperaPage(
                    store: store,
                  );
                case SacPageEtapa.homeChat:
                  return HomeChatSAC(
                    store: store,
                  );
                case SacPageEtapa.finalizado:
                  return AvisoSacPage(
                    store: store,
                  );
              }
            },
          ),
        ),
      ),
    );
  }
}
