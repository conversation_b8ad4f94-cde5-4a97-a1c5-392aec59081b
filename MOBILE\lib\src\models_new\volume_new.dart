// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:map_fields/map_fields.dart';

class VolumeNew {
  final int idos;
  final String os;
  VolumeNew({
    required this.idos,
    required this.os,
  });

  bool find(String search) {
    return os.toLowerCase().trim().contains(search.toLowerCase().trim());
  }

  Map<String, dynamic> toHiveMap() {
    return {
      'idos': idos,
      'os': os,
    };
  }

  factory VolumeNew.fromHiveMap(Map<String, dynamic> map) {
    final v = MapFields.load(map);
    return VolumeNew(
      idos: v.getInt('idos', 0),
      os: v.getString('os', ''),
    );
  }

  factory VolumeNew.fromJson(Map<String, dynamic> map) {
    final v = MapFields.load(map);

    return VolumeNew(
      idos: v.getInt('IDOS', 0),
      os: v.getString('OS', ''),
    );
  }

  VolumeNew copyWith({
    int? idos,
    String? os,
  }) {
    return VolumeNew(
      idos: idos ?? this.idos,
      os: os ?? this.os,
    );
  }
}
