import 'package:asuka/asuka.dart' as asuka;
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:octalog/src/config/flavor_config.dart';
import 'package:octalog/src/pages/splash/splash_page.dart';

class AppWidget extends StatelessWidget {
  const AppWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final flavorConfig = FlavorConfig.instance;

    return MaterialApp(
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('pt', 'BR'),
      ],
      locale: const Locale('pt', 'BR'),
      title: flavorConfig.name,
      home: const SplashPage(),
      builder: asuka.Asuka.builder,
      debugShowCheckedModeBanner: false,
      theme: flavorConfig.theme,
      navigatorObservers: [asuka.Asuka.asukaHeroController],
    );
  }
}
