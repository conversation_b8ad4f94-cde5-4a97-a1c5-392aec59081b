import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:octalog/src/pages/romaneio/romaneio_state.dart';
import 'package:octalog/src/utils/offline_helper.dart';

import '../../helpers/web_connector.dart';
import 'model/romaneio_model.dart';

class RomaneioStore {
  final state = ValueNotifier<RomaneioState>(RomaneioState(
    romaneios: [],
    loading: false,
    error: false,
  ));

  Future setRomaneios() async {
    setLoading(true);
    try {
      final res = await WebConnector().get(
        '/relatorio/romaneio',
      );

      List<RomaneioModel> romaneios =
          (res.data as List).map((e) => RomaneioModel.fromJson(e)).toList();
      state.value = state.value.copyWith(romaneios: romaneios);
    } catch (e) {
      log(e.toString());
      state.value = state.value.copyWith(error: true);
    }

    offlineStore.setOffline(false);
    setLoading(false);
  }

  void setLoading(bool value) {
    state.value = state.value.copyWith(loading: value);
  }
}
